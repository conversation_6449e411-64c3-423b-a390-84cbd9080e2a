








































































































































































































































































































/* 页面容器 */
.container.data-v-57280228 {
	min-height: 100vh;
	background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
	padding: 20rpx;
}
/* 页面头部 */
.header.data-v-57280228 {
	padding: 40rpx 0;
	color: #ffffff;
}
.header-content.data-v-57280228 {
	display: flex;
	justify-content: space-between;
	align-items: center;
	padding: 0 20rpx;
}
.header-info.data-v-57280228 {
	flex: 1;
}
.header-title.data-v-57280228 {
	font-size: 56rpx;
	font-weight: bold;
	margin-bottom: 15rpx;
	text-shadow: 0 2rpx 4rpx rgba(0, 0, 0, 0.3);
}
.header-subtitle.data-v-57280228 {
	font-size: 32rpx;
	opacity: 0.9;
}
/* 用户信息 */
.header-user.data-v-57280228 {
	display: flex;
	align-items: center;
	background: rgba(255, 255, 255, 0.1);
	-webkit-backdrop-filter: blur(10px);
	        backdrop-filter: blur(10px);
	border-radius: 50rpx;
	padding: 15rpx 25rpx;
	cursor: pointer;
	transition: all 0.3s ease;
}
.header-user.data-v-57280228:hover {
	background: rgba(255, 255, 255, 0.2);
	-webkit-transform: translateY(-2rpx);
	        transform: translateY(-2rpx);
}
.user-avatar.data-v-57280228 {
	width: 60rpx;
	height: 60rpx;
	background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
	border-radius: 50%;
	display: flex;
	align-items: center;
	justify-content: center;
	font-size: 28rpx;
	font-weight: bold;
	color: #ffffff;
	margin-right: 15rpx;
	box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.1);
}
.user-name.data-v-57280228 {
	font-size: 28rpx;
	font-weight: bold;
	margin-right: 10rpx;
	max-width: 120rpx;
	overflow: hidden;
	text-overflow: ellipsis;
	white-space: nowrap;
}
.user-arrow.data-v-57280228 {
	font-size: 20rpx;
	opacity: 0.8;
	transition: -webkit-transform 0.3s ease;
	transition: transform 0.3s ease;
	transition: transform 0.3s ease, -webkit-transform 0.3s ease;
}
.header-user:hover .user-arrow.data-v-57280228 {
	-webkit-transform: rotate(180deg);
	        transform: rotate(180deg);
}
/* 导航卡片容器 */
.nav-cards.data-v-57280228 {
	display: flex;
	flex-direction: column;
	gap: 30rpx;
	margin-bottom: 40rpx;
}
/* 导航卡片 */
.nav-card.data-v-57280228 {
	background-color: #ffffff;
	border-radius: 20rpx;
	padding: 40rpx 30rpx;
	box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.1);
	transition: all 0.3s ease;
	cursor: pointer;
	position: relative;
	overflow: hidden;
}
.nav-card.data-v-57280228:hover {
	-webkit-transform: translateY(-6rpx);
	        transform: translateY(-6rpx);
	box-shadow: 0 12rpx 40rpx rgba(0, 0, 0, 0.15);
}
.nav-card.data-v-57280228::before {
	content: '';
	position: absolute;
	top: 0;
	left: 0;
	right: 0;
	height: 6rpx;
	background: linear-gradient(90deg, #667eea 0%, #764ba2 100%);
}
.activation-card.data-v-57280228::before {
	background: linear-gradient(90deg, #f093fb 0%, #f5576c 100%);
}
.signin-card.data-v-57280228::before {
	background: linear-gradient(90deg, #4facfe 0%, #00f2fe 100%);
}
.nav-card-icon.data-v-57280228 {
	font-size: 60rpx;
	margin-bottom: 20rpx;
}
.nav-card-title.data-v-57280228 {
	font-size: 36rpx;
	font-weight: bold;
	color: #333333;
	margin-bottom: 15rpx;
}
.nav-card-desc.data-v-57280228 {
	font-size: 28rpx;
	color: #666666;
	line-height: 1.5;
	margin-bottom: 20rpx;
}
.nav-card-arrow.data-v-57280228 {
	position: absolute;
	right: 30rpx;
	top: 50%;
	-webkit-transform: translateY(-50%);
	        transform: translateY(-50%);
	font-size: 40rpx;
	color: #409EFF;
	font-weight: bold;
}
/* 快速状态 */
.quick-status.data-v-57280228 {
	background-color: #ffffff;
	border-radius: 16rpx;
	box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.1);
	margin-bottom: 30rpx;
	overflow: hidden;
}
.status-header.data-v-57280228 {
	padding: 30rpx;
	border-bottom: 1rpx solid #f0f0f0;
	background: linear-gradient(90deg, #f8f9fa 0%, #ffffff 100%);
}
.status-title.data-v-57280228 {
	font-size: 32rpx;
	font-weight: bold;
	color: #333333;
}
.status-content.data-v-57280228 {
	padding: 30rpx;
}
.status-item.data-v-57280228 {
	display: flex;
	justify-content: space-between;
	align-items: center;
	padding: 15rpx 0;
	border-bottom: 1rpx solid #f5f5f5;
}
.status-item.data-v-57280228:last-child {
	border-bottom: none;
}
.status-label.data-v-57280228 {
	font-size: 28rpx;
	color: #666666;
}
.status-value.data-v-57280228 {
	font-size: 28rpx;
	font-weight: bold;
	color: #333333;
}
.status-value.success.data-v-57280228 {
	color: #67C23A;
}
.status-value.warning.data-v-57280228 {
	color: #E6A23C;
}
/* 未验证提示 */
.no-user-tip.data-v-57280228 {
	background-color: #ffffff;
	border-radius: 16rpx;
	padding: 50rpx 30rpx;
	text-align: center;
	box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.1);
}
.tip-icon.data-v-57280228 {
	font-size: 80rpx;
	margin-bottom: 30rpx;
}
.tip-text.data-v-57280228 {
	font-size: 28rpx;
	color: #666666;
	margin-bottom: 40rpx;
	line-height: 1.6;
}
.tip-buttons.data-v-57280228 {
	display: flex;
	gap: 20rpx;
	justify-content: center;
}
.tip-buttons .btn.data-v-57280228 {
	flex: 1;
	max-width: 300rpx;
}
/* 按钮样式 */
.btn.data-v-57280228 {
	padding: 25rpx 50rpx;
	border-radius: 30rpx;
	border: none;
	font-size: 30rpx;
	font-weight: bold;
	cursor: pointer;
	transition: all 0.3s ease;
	text-align: center;
	display: inline-block;
	box-shadow: 0 6rpx 20rpx rgba(0, 0, 0, 0.1);
}
.btn.data-v-57280228:hover {
	-webkit-transform: translateY(-2rpx);
	        transform: translateY(-2rpx);
	box-shadow: 0 8rpx 25rpx rgba(0, 0, 0, 0.15);
}
.btn-primary.data-v-57280228 {
	background: linear-gradient(135deg, #409EFF 0%, #66b1ff 100%);
	color: #ffffff;
}
.btn-secondary.data-v-57280228 {
	background: linear-gradient(135deg, #909399 0%, #a6a9ad 100%);
	color: #ffffff;
}
/* 动画效果 */
.fade-in.data-v-57280228 {
	-webkit-animation: fadeInUp-data-v-57280228 0.6s ease-out;
	        animation: fadeInUp-data-v-57280228 0.6s ease-out;
}
@-webkit-keyframes fadeInUp-data-v-57280228 {
from {
		opacity: 0;
		-webkit-transform: translateY(30rpx);
		        transform: translateY(30rpx);
}
to {
		opacity: 1;
		-webkit-transform: translateY(0);
		        transform: translateY(0);
}
}
@keyframes fadeInUp-data-v-57280228 {
from {
		opacity: 0;
		-webkit-transform: translateY(30rpx);
		        transform: translateY(30rpx);
}
to {
		opacity: 1;
		-webkit-transform: translateY(0);
		        transform: translateY(0);
}
}
/* 用户菜单弹窗 */
.modal-overlay.data-v-57280228 {
	position: fixed;
	top: 0;
	left: 0;
	right: 0;
	bottom: 0;
	background-color: rgba(0, 0, 0, 0.5);
	display: flex;
	justify-content: center;
	align-items: center;
	z-index: 1000;
	-webkit-animation: fadeIn-data-v-57280228 0.3s ease-out;
	        animation: fadeIn-data-v-57280228 0.3s ease-out;
}
@-webkit-keyframes fadeIn-data-v-57280228 {
from {
		opacity: 0;
}
to {
		opacity: 1;
}
}
@keyframes fadeIn-data-v-57280228 {
from {
		opacity: 0;
}
to {
		opacity: 1;
}
}
.user-menu-content.data-v-57280228 {
	background: #ffffff;
	border-radius: 20rpx;
	width: 90%;
	max-width: 500rpx;
	box-shadow: 0 20rpx 60rpx rgba(0, 0, 0, 0.2);
	-webkit-animation: slideInUp-data-v-57280228 0.3s ease-out;
	        animation: slideInUp-data-v-57280228 0.3s ease-out;
	overflow: hidden;
}
@-webkit-keyframes slideInUp-data-v-57280228 {
from {
		opacity: 0;
		-webkit-transform: translateY(50rpx);
		        transform: translateY(50rpx);
}
to {
		opacity: 1;
		-webkit-transform: translateY(0);
		        transform: translateY(0);
}
}
@keyframes slideInUp-data-v-57280228 {
from {
		opacity: 0;
		-webkit-transform: translateY(50rpx);
		        transform: translateY(50rpx);
}
to {
		opacity: 1;
		-webkit-transform: translateY(0);
		        transform: translateY(0);
}
}
.user-menu-header.data-v-57280228 {
	display: flex;
	align-items: center;
	padding: 40rpx 30rpx;
	background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
	color: #ffffff;
}
.menu-avatar.data-v-57280228 {
	width: 80rpx;
	height: 80rpx;
	background: rgba(255, 255, 255, 0.2);
	border-radius: 50%;
	display: flex;
	align-items: center;
	justify-content: center;
	font-size: 36rpx;
	font-weight: bold;
	margin-right: 20rpx;
	border: 3rpx solid rgba(255, 255, 255, 0.3);
}
.menu-user-info.data-v-57280228 {
	flex: 1;
}
.menu-username.data-v-57280228 {
	font-size: 32rpx;
	font-weight: bold;
	margin-bottom: 8rpx;
}
.menu-user-id.data-v-57280228 {
	font-size: 24rpx;
	opacity: 0.8;
}
.user-menu-list.data-v-57280228 {
	padding: 20rpx 0;
}
.menu-item.data-v-57280228 {
	display: flex;
	align-items: center;
	padding: 25rpx 30rpx;
	cursor: pointer;
	transition: all 0.3s ease;
	border-bottom: 1rpx solid #f5f5f5;
}
.menu-item.data-v-57280228:hover {
	background-color: #f8f9fa;
}
.menu-item.data-v-57280228:last-child {
	border-bottom: none;
}
.menu-item.logout-item.data-v-57280228 {
	color: #f56c6c;
}
.menu-item.logout-item.data-v-57280228:hover {
	background-color: #fef0f0;
}
.menu-icon.data-v-57280228 {
	font-size: 32rpx;
	margin-right: 20rpx;
	width: 40rpx;
	text-align: center;
}
.menu-text.data-v-57280228 {
	flex: 1;
	font-size: 28rpx;
	font-weight: 500;
}
.menu-arrow.data-v-57280228 {
	font-size: 24rpx;
	color: #999999;
	transition: -webkit-transform 0.3s ease;
	transition: transform 0.3s ease;
	transition: transform 0.3s ease, -webkit-transform 0.3s ease;
}
.menu-item:hover .menu-arrow.data-v-57280228 {
	-webkit-transform: translateX(5rpx);
	        transform: translateX(5rpx);
}

