{"version": 3, "sources": ["uni-app:///main.js", "webpack:///D:/projects/HBuilder/fq_sign_uiapp/pages/login/login.vue?944e", "webpack:///D:/projects/HBuilder/fq_sign_uiapp/pages/login/login.vue?e037", "webpack:///D:/projects/HBuilder/fq_sign_uiapp/pages/login/login.vue?3ca0", "webpack:///D:/projects/HBuilder/fq_sign_uiapp/pages/login/login.vue?78d2", "uni-app:///pages/login/login.vue", "webpack:///D:/projects/HBuilder/fq_sign_uiapp/pages/login/login.vue?c2ce", "webpack:///D:/projects/HBuilder/fq_sign_uiapp/pages/login/login.vue?6ea9"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page", "data", "activeTab", "passwordForm", "username", "password", "code", "uuid", "rememberMe", "phoneForm", "phone", "phoneCode", "passwordLoading", "phoneLoading", "showPassword", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "codeUrl", "smsDisabled", "smsButtonText", "smsCountdown", "key", "onLoad", "methods", "showErrorMessage", "console", "uni", "title", "content", "showCancel", "confirmText", "setTimeout", "icon", "duration", "mask", "initPage", "switchTab", "togglePassword", "toggleRemember", "refreshCaptcha", "res", "loadRememberedPassword", "saveRememberedPassword", "encryptData", "mode", "padding", "Pkcs7", "decryptData", "handlePasswordLogin", "token", "userInfo", "userId", "avatar", "roles", "savedToken", "savedUserInfo", "url", "success", "fail", "errorMessage", "sendSmsCode", "startSmsCountdown", "clearInterval", "handlePhoneLogin", "goToRegister"], "mappings": ";;;;;;;;;;;;;AAAA;AAGA;AACA;AAHA;AACAA,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG1DC,UAAU,CAACC,cAAI,CAAC,C;;;;;;;;;;;;;ACLhB;AAAA;AAAA;AAAA;AAAA;AAAA;AAA8H;AAC9H;AACyD;AACL;AACqC;;;AAGzF;AACoL;AACpL,gBAAgB,2LAAU;AAC1B,EAAE,2EAAM;AACR,EAAE,4FAAM;AACR,EAAE,qGAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,gGAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;ACRA;AAAA;AAAA;AAAA;AAAurB,CAAgB,qrBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;ACwG3sB;AAMA;AAGA;AAMA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;eAEA;EACAC;IACA;MACA;MACAC;MAEA;MACAC;QACAC;QACAC;QACAC;QACAC;QACAC;MACA;MAEA;MACAC;QACAC;QACAC;MACA;MAEA;MACAC;MACAC;MACAC;MACAC;MACAC;MAEA;MACAC;MACAC;MACAC;MACAC;IACA;EACA;EAEAC;IACA;EACA;EAEAC;IACA;IACAC;MAAA;MACAC;MAEA;QACAC;UACAC;UACAC;UACAC;UACAC;QACA;MACA;QACA;QACAJ;QACAK;UACAL;YACAC;YACAK;YACAC;YACAC;UACA;QACA;MACA;IACA;IAEA;IACAC;MAAA;MAAA;QAAA;UAAA;YAAA;cAAA;gBAAA;gBAAA,OAEA;cAAA;gBACA;gBACA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IACA;IAEA;IACAC;MACA;IACA;IAEA;IACAC;MACA;IACA;IAEA;IACAC;MACA;IACA;IAEA;IACAC;MAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBAAA;gBAAA;gBAAA,OAEA;cAAA;gBAAAC;gBACA;kBACA;kBACA;oBACA;oBACA;kBACA;gBACA;gBAAA;gBAAA;cAAA;gBAAA;gBAAA;gBAEAf;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IAEA;IAEA;IACAgB;MACA;QACA;QACA;QACA;QAEA;UACA;UACA;UACA;YACA;YACA;UACA;YACAhB;YACA;UACA;UACA;QACA;MACA;QACAA;MACA;IACA;IAEA;IACAiB;MACA;QACA;UACAhB;UACA;UACA;YACA;YACA;YACAA;UACA;YACAD;YACA;YACAC;UACA;UACAA;QACA;UACAA;UACAA;UACAA;QACA;MACA;QACAD;QACA;MACA;IACA;IAGAkB;MACA;MACA;MACA;QACAC;QACAC,kCACAC;MACA;MACA;IACA;IAEAC;MACA;MACA;MACA;QACAH;QACAC,kCACAC;MACA;MACA;IACA;IACA;IACAE;MAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBAAA,IACA;kBAAA;kBAAA;gBAAA;gBACAtB;kBACAC;kBACAK;gBACA;gBAAA;cAAA;gBAAA,IAIA;kBAAA;kBAAA;gBAAA;gBACAN;kBACAC;kBACAK;gBACA;gBAAA;cAAA;gBAAA,MAIA;kBAAA;kBAAA;gBAAA;gBACAN;kBACAC;kBACAK;gBACA;gBAAA;cAAA;gBAIA;gBAAA;gBAAA;gBAAA,OAGA,oBACA,8BACA,8BACA,0BACA,yBACA;cAAA;gBALAQ;gBAAA,IAQAA;kBAAA;kBAAA;gBAAA;gBAAA,MACA;cAAA;gBAAA,MAGAA;kBAAA;kBAAA;gBAAA;gBAAA;gBAKA;gBACAS;gBACA;kBACAA;gBACA;kBACAA;gBACA;kBACAA;gBACA;gBAAA,IAGAA;kBAAA;kBAAA;gBAAA;gBAAA,MACA;cAAA;gBAGA;gBACA;;gBAEA;gBACAC;kBACA7C,2CACAmC,iCACA,gCACA;kBACAW,uCACAX,2BACAA,2BACA;kBACAY,uCACAZ,+BACA;kBACAa,qCACAb;kBACA7B,qCACA6B,8BACA;gBACA;gBAEA;;gBAEA;gBACAc;gBACAC;gBAAA,IAEAD;kBAAA;kBAAA;gBAAA;gBAAA,MACA;cAAA;gBAAA,IAEAC;kBAAA;kBAAA;gBAAA;gBAAA,MACA;cAAA;gBAAA;gBAAA;cAAA;gBAAA;gBAAA;gBAGA9B;gBACAC;kBACAC;kBACAK;kBACAC;gBACA;gBAAA;cAAA;gBAIA;gBACA;kBACA;gBACA;kBACAR;gBACA;gBAEAC;kBACAC;kBACAK;kBACAC;gBACA;;gBAEA;gBACAF;kBACA;kBACA;kBACA;kBAEA;oBACAN;oBACAC;sBACAC;sBACAK;oBACA;oBACA;kBACA;;kBAEA;kBACA;kBAEAN;oBACA8B;oBACAC;oBACAC;sBACAjC;oBACA;kBACA;gBACA;gBAAA;gBAAA;cAAA;gBAEAA;;gBAEA;gBACAkC;gBACA;kBACAA;gBACA;kBACAA;gBACA;kBACAA;gBACA;kBACAA;gBACA;gBAEAjC;kBACAC;kBACAK;kBACAC;gBACA;;gBAEA;gBAAA,KACA;kBAAA;kBAAA;gBAAA;gBAAA;gBAAA,OACA;cAAA;gBAAA;gBAAA;cAAA;gBAAA;gBAAA;gBAIAR;gBAEAkC,8BAEA;gBACA;kBACA;oBACAA;kBACA;oBACAA;kBACA;oBACAA;kBACA;oBACAA;kBACA;gBACA;;gBAEA;gBACA;;gBAEA;gBAAA,KACA;kBAAA;kBAAA;gBAAA;gBAAA;gBAAA,OACA;cAAA;gBAAA;gBAGA;gBAAA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IAEA;IAEA;IACAC;MAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBAAA,IACA;kBAAA;kBAAA;gBAAA;gBACAlC;kBACAC;kBACAK;gBACA;gBAAA;cAAA;gBAAA,IAIA;kBAAA;kBAAA;gBAAA;gBACAN;kBACAC;kBACAK;gBACA;gBAAA;cAAA;gBAAA;gBAAA;gBAAA,OAKA;cAAA;gBAAAQ;gBACA;kBACAd;oBACAC;oBACAK;kBACA;kBACA;gBACA;kBACAN;oBACAC;oBACAK;kBACA;gBACA;gBAAA;gBAAA;cAAA;gBAAA;gBAAA;gBAEAP;gBACAC;kBACAC;kBACAK;gBACA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IAEA;IAEA;IACA6B;MAAA;MACA;MACA;MACA;MAEA;QACA;QACA;QAEA;UACAC;UACA;UACA;QACA;MACA;IACA;IAEA;IACAC;MAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBAAA,IACA;kBAAA;kBAAA;gBAAA;gBACArC;kBACAC;kBACAK;gBACA;gBAAA;cAAA;gBAAA,IAIA;kBAAA;kBAAA;gBAAA;gBACAN;kBACAC;kBACAK;gBACA;gBAAA;cAAA;gBAAA,IAIA;kBAAA;kBAAA;gBAAA;gBACAN;kBACAC;kBACAK;gBACA;gBAAA;cAAA;gBAIA;gBAAA;gBAAA;gBAAA,OAGA;cAAA;gBAAAQ;gBAAA,MAEAA;kBAAA;kBAAA;gBAAA;gBAAA;gBAIA;gBACAS;gBACA;kBACAA;gBACA;kBACAA;gBACA;kBACAA;gBACA;gBAAA,IAGAA;kBAAA;kBAAA;gBAAA;gBAAA,MACA;cAAA;gBAGA;gBACA;;gBAEA;gBACAC;kBACA7C,2CACAmC,iCACA,0BACA;kBACAW,uCACAX,2BACAA,2BACA;kBACAY,uCACAZ,+BACA;kBACAa,qCACAb;kBACA7B;gBACA;gBAEA;;gBAEA;gBACA2C;gBACAC;gBAAA,IAEAD;kBAAA;kBAAA;gBAAA;gBAAA,MACA;cAAA;gBAAA,IAEAC;kBAAA;kBAAA;gBAAA;gBAAA,MACA;cAAA;gBAAA;gBAAA;cAAA;gBAAA;gBAAA;gBAGA9B;gBACAC;kBACAC;kBACAK;kBACAC;gBACA;gBAAA;cAAA;gBAIAP;kBACAC;kBACAK;kBACAC;gBACA;;gBAEA;gBACAF;kBACA;kBACA;kBACA;kBAEA;oBACAN;oBACAC;sBACAC;sBACAK;oBACA;oBACA;kBACA;;kBAEA;kBACA;kBAEAN;oBACA8B;oBACAC;oBACAC;sBACAjC;oBACA;kBACA;gBACA;gBAAA;gBAAA;cAAA;gBAEAA;;gBAEA;gBACAkC;gBACA;kBACAA;gBACA;kBACAA;gBACA;kBACAA;gBACA;kBACAA;gBACA;gBAEAjC;kBACAC;kBACAK;kBACAC;gBACA;cAAA;gBAAA;gBAAA;cAAA;gBAAA;gBAAA;gBAGAR;gBAEAkC,+BAEA;gBACA;kBACA;oBACAA;kBACA;oBACAA;kBACA;oBACAA;kBACA;oBACAA;kBACA;gBACA;gBAEAjC;kBACAC;kBACAK;kBACAC;gBACA;cAAA;gBAAA;gBAEA;gBAAA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IAEA;IAEA;IACA+B;MACAtC;QACA8B;MACA;IACA;EACA;AACA;AAAA,2B;;;;;;;;;;;;;ACpuBA;AAAA;AAAA;AAAA;AAAogC,CAAgB,89BAAG,EAAC,C;;;;;;;;;;;ACAxhC;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "pages/login/login.js", "sourcesContent": ["import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;\nimport Vue from 'vue'\nimport Page from './pages/login/login.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./login.vue?vue&type=template&id=b237504c&scoped=true&\"\nvar renderjs\nimport script from \"./login.vue?vue&type=script&lang=js&\"\nexport * from \"./login.vue?vue&type=script&lang=js&\"\nimport style0 from \"./login.vue?vue&type=style&index=0&id=b237504c&scoped=true&lang=css&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../../app/HBuilderX/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"b237504c\",\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"pages/login/login.vue\"\nexport default component.exports", "export * from \"-!../../../../../app/HBuilderX/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../../../app/HBuilderX/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--17-0!../../../../../app/HBuilderX/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!../../../../../app/HBuilderX/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!../../../../../app/HBuilderX/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../app/HBuilderX/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./login.vue?vue&type=template&id=b237504c&scoped=true&\"", "var components\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!../../../../../app/HBuilderX/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../app/HBuilderX/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../app/HBuilderX/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../app/HBuilderX/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../app/HBuilderX/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./login.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../../../app/HBuilderX/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../app/HBuilderX/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../app/HBuilderX/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../app/HBuilderX/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../app/HBuilderX/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./login.vue?vue&type=script&lang=js&\"", "<template>\r\n\t<view class=\"login-container\">\r\n\t\t<!-- 背景装饰 -->\r\n\t\t<view class=\"bg-decoration\">\r\n\t\t\t<view class=\"circle circle-1\"></view>\r\n\t\t\t<view class=\"circle circle-2\"></view>\r\n\t\t\t<view class=\"circle circle-3\"></view>\r\n\t\t</view>\r\n\r\n\t\t<!-- 登录卡片 -->\r\n\t\t<view class=\"login-card fade-in\">\r\n\t\t\t<!-- 头部 -->\r\n\t\t\t<view class=\"login-header\">\r\n\t\t\t\t<view class=\"logo\">🔐</view>\r\n\t\t\t\t<view class=\"title\">欢迎使用本系统</view>\r\n\t\t\t\t<view class=\"subtitle\">请选择登录方式</view>\r\n\t\t\t</view>\r\n\r\n\t\t\t<!-- 标签页切换 -->\r\n\t\t\t<view class=\"tab-container\">\r\n\t\t\t\t<view class=\"tab-item\" :class=\"{ active: activeTab === 'password' }\" @click=\"switchTab('password')\">\r\n\t\t\t\t\t密码登录\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class=\"tab-item\" :class=\"{ active: activeTab === 'phone' }\" @click=\"switchTab('phone')\">\r\n\t\t\t\t\t手机登录\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\r\n\t\t\t<!-- 密码登录表单 -->\r\n\t\t\t<view v-if=\"activeTab === 'password'\" class=\"form-container\">\r\n\t\t\t\t<view class=\"input-group\">\r\n\t\t\t\t\t<view class=\"input-icon\">👤</view>\r\n\t\t\t\t\t<input v-model=\"passwordForm.username\" placeholder=\"请输入用户名/手机号\" class=\"input-field\" />\r\n\t\t\t\t</view>\r\n\r\n\t\t\t\t<view class=\"input-group\">\r\n\t\t\t\t\t<view class=\"input-icon\">🔒</view>\r\n\t\t\t\t\t<input v-model=\"passwordForm.password\" placeholder=\"请输入密码\" class=\"input-field\"\r\n\t\t\t\t\t\t:password=\"!showPassword\" />\r\n\t\t\t\t\t<view class=\"input-suffix\" @click=\"togglePassword\">\r\n\t\t\t\t\t\t{{ showPassword ? '🙈' : '👁️' }}\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\r\n\t\t\t\t<!-- 验证码 -->\r\n\t\t\t\t<view v-if=\"captchaEnabled\" class=\"input-group captcha-group\">\r\n\t\t\t\t\t<view class=\"input-icon\">🔢</view>\r\n\t\t\t\t\t<input v-model=\"passwordForm.code\" placeholder=\"请输入验证码\" class=\"input-field captcha-input\" />\r\n\t\t\t\t\t<view class=\"captcha-image\" @click=\"refreshCaptcha\">\r\n\t\t\t\t\t\t<image v-if=\"codeUrl\" :src=\"codeUrl\" class=\"captcha-img\" mode=\"aspectFit\" />\r\n\t\t\t\t\t\t<view v-else class=\"captcha-placeholder\">点击获取</view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\r\n\t\t\t\t<!-- 记住密码 -->\r\n\t\t\t\t<view class=\"checkbox-group\">\r\n\t\t\t\t\t<view class=\"checkbox\" :class=\"{ checked: passwordForm.rememberMe }\" @click=\"toggleRemember\">\r\n\t\t\t\t\t\t<view class=\"checkbox-icon\">{{ passwordForm.rememberMe ? '✓' : '' }}</view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<text class=\"checkbox-label\">记住密码</text>\r\n\t\t\t\t</view>\r\n\r\n\t\t\t\t<!-- 登录按钮 -->\r\n\t\t\t\t<button class=\"login-btn\" :class=\"{ loading: passwordLoading }\" :disabled=\"passwordLoading\"\r\n\t\t\t\t\t@click=\"handlePasswordLogin\">\r\n\t\t\t\t\t<view v-if=\"passwordLoading\" class=\"loading-icon\">⏳</view>\r\n\t\t\t\t\t{{ passwordLoading ? '登录中...' : '登录' }}\r\n\t\t\t\t</button>\r\n\t\t\t</view>\r\n\r\n\t\t\t<!-- 手机登录表单 -->\r\n\t\t\t<view v-if=\"activeTab === 'phone'\" class=\"form-container\">\r\n\t\t\t\t<view class=\"input-group\">\r\n\t\t\t\t\t<view class=\"input-icon\">📱</view>\r\n\t\t\t\t\t<input v-model=\"phoneForm.phone\" placeholder=\"请输入手机号\" class=\"input-field\" type=\"number\" />\r\n\t\t\t\t</view>\r\n\r\n\t\t\t\t<view class=\"input-group sms-group\">\r\n\t\t\t\t\t<view class=\"input-icon\">💬</view>\r\n\t\t\t\t\t<input v-model=\"phoneForm.phoneCode\" placeholder=\"请输入短信验证码\" class=\"input-field sms-input\" />\r\n\t\t\t\t\t<button class=\"sms-btn\" :disabled=\"smsDisabled\" @click=\"sendSmsCode\">\r\n\t\t\t\t\t\t{{ smsButtonText }}\r\n\t\t\t\t\t</button>\r\n\t\t\t\t</view>\r\n\r\n\t\t\t\t<!-- 登录按钮 -->\r\n\t\t\t\t<button class=\"login-btn\" :class=\"{ loading: phoneLoading }\" :disabled=\"phoneLoading\"\r\n\t\t\t\t\t@click=\"handlePhoneLogin\">\r\n\t\t\t\t\t<view v-if=\"phoneLoading\" class=\"loading-icon\">⏳</view>\r\n\t\t\t\t\t{{ phoneLoading ? '登录中...' : '登录' }}\r\n\t\t\t\t</button>\r\n\t\t\t</view>\r\n\r\n\t\t\t<!-- 底部操作 -->\r\n\t\t\t<view class=\"login-footer\">\r\n\t\t\t\t<view class=\"footer-link\" @click=\"goToRegister\">\r\n\t\t\t\t\t还没有账号？立即注册\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t</view>\r\n\t</view>\r\n</template>\r\n\r\n<script>\r\n\timport {\r\n\t\tloginApi,\r\n\t\tphoneLogin,\r\n\t\tgetCodeImg,\r\n\t\tsendPhoneCode\r\n\t} from '@/api/user'\r\n\timport {\r\n\t\tutils\r\n\t} from '@/api/index'\r\n\timport {\r\n\t\tsetToken,\r\n\t\tsetUserInfo,\r\n\t\tdisableGuard\r\n\t} from '@/utils/auth'\r\n\r\n\timport CryptoJS from \"@/utils/crypto-js.min.js\"\r\n\r\n\texport default {\r\n\t\tdata() {\r\n\t\t\treturn {\r\n\t\t\t\t// 当前标签页\r\n\t\t\t\tactiveTab: 'password',\r\n\r\n\t\t\t\t// 密码登录表单\r\n\t\t\t\tpasswordForm: {\r\n\t\t\t\t\tusername: '',\r\n\t\t\t\t\tpassword: '',\r\n\t\t\t\t\tcode: '',\r\n\t\t\t\t\tuuid: '',\r\n\t\t\t\t\trememberMe: false\r\n\t\t\t\t},\r\n\r\n\t\t\t\t// 手机登录表单\r\n\t\t\t\tphoneForm: {\r\n\t\t\t\t\tphone: '',\r\n\t\t\t\t\tphoneCode: ''\r\n\t\t\t\t},\r\n\r\n\t\t\t\t// 状态控制\r\n\t\t\t\tpasswordLoading: false,\r\n\t\t\t\tphoneLoading: false,\r\n\t\t\t\tshowPassword: false,\r\n\t\t\t\tcaptchaEnabled: true,\r\n\t\t\t\tcodeUrl: '',\r\n\r\n\t\t\t\t// 短信验证码\r\n\t\t\t\tsmsDisabled: false,\r\n\t\t\t\tsmsButtonText: '获取验证码',\r\n\t\t\t\tsmsCountdown: 0,\r\n\t\t\t\tkey: \"zizaikpgcodezizaikpgcodezizaikpgcodezizaikpgcode\"\r\n\t\t\t}\r\n\t\t},\r\n\r\n\t\tonLoad() {\r\n\t\t\tthis.initPage()\r\n\t\t},\r\n\r\n\t\tmethods: {\r\n\t\t\t// 显示错误提示的可靠方法\r\n\t\t\tshowErrorMessage(message, useModal = false) {\r\n\t\t\t\tconsole.log('显示错误信息:', message)\r\n\r\n\t\t\t\tif (useModal) {\r\n\t\t\t\t\tuni.showModal({\r\n\t\t\t\t\t\ttitle: '提示',\r\n\t\t\t\t\t\tcontent: message,\r\n\t\t\t\t\t\tshowCancel: false,\r\n\t\t\t\t\t\tconfirmText: '我知道了'\r\n\t\t\t\t\t})\r\n\t\t\t\t} else {\r\n\t\t\t\t\t// 确保先隐藏loading，再显示toast\r\n\t\t\t\t\tuni.hideLoading()\r\n\t\t\t\t\tsetTimeout(() => {\r\n\t\t\t\t\t\tuni.showToast({\r\n\t\t\t\t\t\t\ttitle: message,\r\n\t\t\t\t\t\t\ticon: 'none',\r\n\t\t\t\t\t\t\tduration: 3000,\r\n\t\t\t\t\t\t\tmask: true\r\n\t\t\t\t\t\t})\r\n\t\t\t\t\t}, 200)\r\n\t\t\t\t}\r\n\t\t\t},\r\n\r\n\t\t\t// 初始化页面\r\n\t\t\tasync initPage() {\r\n\t\t\t\t// 获取验证码\r\n\t\t\t\tawait this.refreshCaptcha()\r\n\t\t\t\t// 获取记住的密码\r\n\t\t\t\tthis.loadRememberedPassword()\r\n\t\t\t},\r\n\r\n\t\t\t// 切换标签页\r\n\t\t\tswitchTab(tab) {\r\n\t\t\t\tthis.activeTab = tab\r\n\t\t\t},\r\n\r\n\t\t\t// 切换密码显示\r\n\t\t\ttogglePassword() {\r\n\t\t\t\tthis.showPassword = !this.showPassword\r\n\t\t\t},\r\n\r\n\t\t\t// 切换记住密码\r\n\t\t\ttoggleRemember() {\r\n\t\t\t\tthis.passwordForm.rememberMe = !this.passwordForm.rememberMe\r\n\t\t\t},\r\n\r\n\t\t\t// 刷新验证码\r\n\t\t\tasync refreshCaptcha() {\r\n\t\t\t\ttry {\r\n\t\t\t\t\tconst res = await getCodeImg()\r\n\t\t\t\t\tif (res.code === 200) {\r\n\t\t\t\t\t\tthis.captchaEnabled = res.captchaEnabled !== false\r\n\t\t\t\t\t\tif (this.captchaEnabled) {\r\n\t\t\t\t\t\t\tthis.codeUrl = \"data:image/gif;base64,\" + res.img\r\n\t\t\t\t\t\t\tthis.passwordForm.uuid = res.uuid\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t}\r\n\t\t\t\t} catch (error) {\r\n\t\t\t\t\tconsole.error('获取验证码失败:', error)\r\n\t\t\t\t}\r\n\t\t\t},\r\n\r\n\t\t\t// 加载记住的密码\r\n\t\t\tloadRememberedPassword() {\r\n\t\t\t\ttry {\r\n\t\t\t\t\tconst username = uni.getStorageSync('remembered_username')\r\n\t\t\t\t\tconst password = uni.getStorageSync('remembered_password')\r\n\t\t\t\t\tconst rememberMe = uni.getStorageSync('remembered_me')\r\n\r\n\t\t\t\t\tif (rememberMe && username && password) {\r\n\t\t\t\t\t\tthis.passwordForm.username = username\r\n\t\t\t\t\t\t// 尝试解密密码，如果失败则使用原始密码\r\n\t\t\t\t\t\ttry {\r\n\t\t\t\t\t\t\t// this.passwordForm.password = utils.decrypt(password)\r\n\t\t\t\t\t\t\tthis.passwordForm.password = this.decryptData(password)\r\n\t\t\t\t\t\t} catch (error) {\r\n\t\t\t\t\t\t\tconsole.error('密码解密失败:', error)\r\n\t\t\t\t\t\t\tthis.passwordForm.password = password\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t\tthis.passwordForm.rememberMe = true\r\n\t\t\t\t\t}\r\n\t\t\t\t} catch (error) {\r\n\t\t\t\t\tconsole.error('加载记住的密码失败:', error)\r\n\t\t\t\t}\r\n\t\t\t},\r\n\r\n\t\t\t// 保存记住的密码\r\n\t\t\tsaveRememberedPassword() {\r\n\t\t\t\ttry {\r\n\t\t\t\t\tif (this.passwordForm.rememberMe) {\r\n\t\t\t\t\t\tuni.setStorageSync('remembered_username', this.passwordForm.username)\r\n\t\t\t\t\t\t// 尝试加密密码，如果失败则直接存储\r\n\t\t\t\t\t\ttry {\r\n\t\t\t\t\t\t\t// const encryptedPassword = utils.encrypt(this.passwordForm.password)\r\n\t\t\t\t\t\t\tconst encryptedPassword = this.encryptData(this.passwordForm.password);\r\n\t\t\t\t\t\t\tuni.setStorageSync('remembered_password', encryptedPassword)\r\n\t\t\t\t\t\t} catch (error) {\r\n\t\t\t\t\t\t\tconsole.error('密码加密失败，使用原始密码存储:', error)\r\n\t\t\t\t\t\t\t// 如果加密失败，直接存储原始密码（不推荐，但保证功能可用）\r\n\t\t\t\t\t\t\tuni.setStorageSync('remembered_password', this.passwordForm.password)\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t\tuni.setStorageSync('remembered_me', true)\r\n\t\t\t\t\t} else {\r\n\t\t\t\t\t\tuni.removeStorageSync('remembered_username')\r\n\t\t\t\t\t\tuni.removeStorageSync('remembered_password')\r\n\t\t\t\t\t\tuni.removeStorageSync('remembered_me')\r\n\t\t\t\t\t}\r\n\t\t\t\t} catch (error) {\r\n\t\t\t\t\tconsole.error('保存记住的密码失败:', error)\r\n\t\t\t\t\t// 即使保存失败，也不应该影响登录流程\r\n\t\t\t\t}\r\n\t\t\t},\r\n\r\n\r\n\t\t\tencryptData(data) {\r\n\t\t\t\t// 使用3DES加密\r\n\t\t\t\tconst keyHex = CryptoJS.enc.Utf8.parse(this.key);\r\n\t\t\t\tconst encrypted = CryptoJS.TripleDES.encrypt(data, keyHex, {\r\n\t\t\t\t\tmode: CryptoJS.mode.ECB,\r\n\t\t\t\t\tpadding: CryptoJS.pad.\r\n\t\t\t\t\tPkcs7\r\n\t\t\t\t});\r\n\t\t\t\treturn encrypted.toString();\r\n\t\t\t},\r\n\r\n\t\t\tdecryptData(encryptedData) {\r\n\t\t\t\t// 使用3DES解密\r\n\t\t\t\tconst keyHex = CryptoJS.enc.Utf8.parse(this.key);\r\n\t\t\t\tconst decrypted = CryptoJS.TripleDES.decrypt(encryptedData, keyHex, {\r\n\t\t\t\t\tmode: CryptoJS.mode.ECB,\r\n\t\t\t\t\tpadding: CryptoJS.pad.\r\n\t\t\t\t\tPkcs7\r\n\t\t\t\t});\r\n\t\t\t\treturn decrypted.toString(CryptoJS.enc.Utf8);\r\n\t\t\t},\r\n\t\t\t// 密码登录\r\n\t\t\tasync handlePasswordLogin() {\r\n\t\t\t\tif (!this.passwordForm.username) {\r\n\t\t\t\t\tuni.showToast({\r\n\t\t\t\t\t\ttitle: '请输入用户名',\r\n\t\t\t\t\t\ticon: 'none'\r\n\t\t\t\t\t})\r\n\t\t\t\t\treturn\r\n\t\t\t\t}\r\n\r\n\t\t\t\tif (!this.passwordForm.password) {\r\n\t\t\t\t\tuni.showToast({\r\n\t\t\t\t\t\ttitle: '请输入密码',\r\n\t\t\t\t\t\ticon: 'none'\r\n\t\t\t\t\t})\r\n\t\t\t\t\treturn\r\n\t\t\t\t}\r\n\r\n\t\t\t\tif (this.captchaEnabled && !this.passwordForm.code) {\r\n\t\t\t\t\tuni.showToast({\r\n\t\t\t\t\t\ttitle: '请输入验证码',\r\n\t\t\t\t\t\ticon: 'none'\r\n\t\t\t\t\t})\r\n\t\t\t\t\treturn\r\n\t\t\t\t}\r\n\r\n\t\t\t\tthis.passwordLoading = true\r\n\r\n\t\t\t\ttry {\r\n\t\t\t\t\tconst res = await loginApi(\r\n\t\t\t\t\t\tthis.passwordForm.username,\r\n\t\t\t\t\t\tthis.passwordForm.password,\r\n\t\t\t\t\t\tthis.passwordForm.code,\r\n\t\t\t\t\t\tthis.passwordForm.uuid\r\n\t\t\t\t\t)\r\n\r\n\t\t\t\t\t// 检查响应是否有效\r\n\t\t\t\t\tif (!res) {\r\n\t\t\t\t\t\tthrow new Error('服务器无响应')\r\n\t\t\t\t\t}\r\n\r\n\t\t\t\t\tif (res.code === 200 || res.status === 200 || res.success) {\r\n\r\n\t\t\t\t\t\t// 保存登录信息（优先处理，确保登录状态正确保存）\r\n\t\t\t\t\t\ttry {\r\n\r\n\t\t\t\t\t\t\t// 安全地获取token\r\n\t\t\t\t\t\t\tlet token = null\r\n\t\t\t\t\t\t\tif (res.token) {\r\n\t\t\t\t\t\t\t\ttoken = res.token\r\n\t\t\t\t\t\t\t} else if (res.data && res.data.token) {\r\n\t\t\t\t\t\t\t\ttoken = res.data.token\r\n\t\t\t\t\t\t\t} else if (res.access_token) {\r\n\t\t\t\t\t\t\t\ttoken = res.access_token\r\n\t\t\t\t\t\t\t}\r\n\r\n\r\n\t\t\t\t\t\t\tif (!token) {\r\n\t\t\t\t\t\t\t\tthrow new Error('响应中未找到有效的token')\r\n\t\t\t\t\t\t\t}\r\n\r\n\t\t\t\t\t\t\t// 保存token\r\n\t\t\t\t\t\t\tsetToken(token)\r\n\r\n\t\t\t\t\t\t\t// 安全地构造用户信息对象\r\n\t\t\t\t\t\t\tconst userInfo = {\r\n\t\t\t\t\t\t\t\tusername: (res.data && res.data.username) ||\r\n\t\t\t\t\t\t\t\t\t(res.user && res.user.username) ||\r\n\t\t\t\t\t\t\t\t\tthis.passwordForm.username ||\r\n\t\t\t\t\t\t\t\t\t'用户',\r\n\t\t\t\t\t\t\t\tuserId: (res.data && res.data.userId) ||\r\n\t\t\t\t\t\t\t\t\t(res.data && res.data.id) ||\r\n\t\t\t\t\t\t\t\t\t(res.user && res.user.id) ||\r\n\t\t\t\t\t\t\t\t\t'user_' + Date.now(),\r\n\t\t\t\t\t\t\t\tavatar: (res.data && res.data.avatar) ||\r\n\t\t\t\t\t\t\t\t\t(res.user && res.user.avatar) ||\r\n\t\t\t\t\t\t\t\t\t'',\r\n\t\t\t\t\t\t\t\troles: (res.data && res.data.roles) ||\r\n\t\t\t\t\t\t\t\t\t(res.user && res.user.roles) || ['user'],\r\n\t\t\t\t\t\t\t\tphone: (res.data && res.data.phone) ||\r\n\t\t\t\t\t\t\t\t\t(res.user && res.user.phone) ||\r\n\t\t\t\t\t\t\t\t\t''\r\n\t\t\t\t\t\t\t}\r\n\r\n\t\t\t\t\t\t\tsetUserInfo(userInfo)\r\n\r\n\t\t\t\t\t\t\t// 验证保存结果\r\n\t\t\t\t\t\t\tconst savedToken = uni.getStorageSync('user_token')\r\n\t\t\t\t\t\t\tconst savedUserInfo = uni.getStorageSync('user_info')\r\n\r\n\t\t\t\t\t\t\tif (!savedToken) {\r\n\t\t\t\t\t\t\t\tthrow new Error('Token保存失败')\r\n\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\tif (!savedUserInfo) {\r\n\t\t\t\t\t\t\t\tthrow new Error('用户信息保存失败')\r\n\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t} catch (error) {\r\n\t\t\t\t\t\t\tconsole.error('保存登录信息失败:', error)\r\n\t\t\t\t\t\t\tuni.showToast({\r\n\t\t\t\t\t\t\t\ttitle: '登录状态保存失败: ' + error.message,\r\n\t\t\t\t\t\t\t\ticon: 'none',\r\n\t\t\t\t\t\t\t\tduration: 3000\r\n\t\t\t\t\t\t\t})\r\n\t\t\t\t\t\t\treturn\r\n\t\t\t\t\t\t}\r\n\r\n\t\t\t\t\t\t// 保存记住的密码（不影响登录流程）\r\n\t\t\t\t\t\ttry {\r\n\t\t\t\t\t\t\tthis.saveRememberedPassword()\r\n\t\t\t\t\t\t} catch (error) {\r\n\t\t\t\t\t\t\tconsole.error('保存记住密码失败，但不影响登录:', error)\r\n\t\t\t\t\t\t}\r\n\r\n\t\t\t\t\t\tuni.showToast({\r\n\t\t\t\t\t\t\ttitle: '登录成功',\r\n\t\t\t\t\t\t\ticon: 'success',\r\n\t\t\t\t\t\t\tduration: 1000\r\n\t\t\t\t\t\t})\r\n\r\n\t\t\t\t\t\t// 确保token保存后再跳转\r\n\t\t\t\t\t\tsetTimeout(() => {\r\n\t\t\t\t\t\t\t// 再次验证登录状态\r\n\t\t\t\t\t\t\tconst finalToken = uni.getStorageSync('user_token')\r\n\t\t\t\t\t\t\tconst finalUserInfo = uni.getStorageSync('user_info')\r\n\r\n\t\t\t\t\t\t\tif (!finalToken || !finalUserInfo) {\r\n\t\t\t\t\t\t\t\tconsole.error('登录信息验证失败，无法跳转')\r\n\t\t\t\t\t\t\t\tuni.showToast({\r\n\t\t\t\t\t\t\t\t\ttitle: '登录状态异常，请重试',\r\n\t\t\t\t\t\t\t\t\ticon: 'none'\r\n\t\t\t\t\t\t\t\t})\r\n\t\t\t\t\t\t\t\treturn\r\n\t\t\t\t\t\t\t}\r\n\r\n\t\t\t\t\t\t\t// 临时禁用路由守卫，避免跳转时被拦截\r\n\t\t\t\t\t\t\tdisableGuard()\r\n\r\n\t\t\t\t\t\t\tuni.reLaunch({\r\n\t\t\t\t\t\t\t\turl: '/pages/index/index',\r\n\t\t\t\t\t\t\t\tsuccess: () => {},\r\n\t\t\t\t\t\t\t\tfail: (error) => {\r\n\t\t\t\t\t\t\t\t\tconsole.error('跳转首页失败:', error)\r\n\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t})\r\n\t\t\t\t\t\t}, 1200)\r\n\t\t\t\t\t} else {\r\n\t\t\t\t\t\tconsole.error('登录失败:', res)\r\n\r\n\t\t\t\t\t\t// 提取错误信息\r\n\t\t\t\t\t\tlet errorMessage = '登录失败'\r\n\t\t\t\t\t\tif (res.msg) {\r\n\t\t\t\t\t\t\terrorMessage = res.msg\r\n\t\t\t\t\t\t} else if (res.message) {\r\n\t\t\t\t\t\t\terrorMessage = res.message\r\n\t\t\t\t\t\t} else if (res.error) {\r\n\t\t\t\t\t\t\terrorMessage = res.error\r\n\t\t\t\t\t\t} else if (res.data && res.data.message) {\r\n\t\t\t\t\t\t\terrorMessage = res.data.message\r\n\t\t\t\t\t\t}\r\n\r\n\t\t\t\t\t\tuni.showToast({\r\n\t\t\t\t\t\t\ttitle: errorMessage,\r\n\t\t\t\t\t\t\ticon: 'none',\r\n\t\t\t\t\t\t\tduration: 3000\r\n\t\t\t\t\t\t})\r\n\r\n\t\t\t\t\t\t// 刷新验证码\r\n\t\t\t\t\t\tif (this.captchaEnabled) {\r\n\t\t\t\t\t\t\tawait this.refreshCaptcha()\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t}\r\n\t\t\t\t} catch (error) {\r\n\t\t\t\t\tconsole.error('登录请求失败:', error)\r\n\r\n\t\t\t\t\tlet errorMessage = '登录失败，请稍后再试'\r\n\r\n\t\t\t\t\t// 根据错误类型提供更具体的错误信息\r\n\t\t\t\t\tif (error.message) {\r\n\t\t\t\t\t\tif (error.message.includes('请先配置正确的 API 地址')) {\r\n\t\t\t\t\t\t\terrorMessage = 'API地址未配置，请联系管理员'\r\n\t\t\t\t\t\t} else if (error.message.includes('网络')) {\r\n\t\t\t\t\t\t\terrorMessage = '网络连接失败，请检查网络'\r\n\t\t\t\t\t\t} else if (error.message.includes('超时')) {\r\n\t\t\t\t\t\t\terrorMessage = '请求超时，请重试'\r\n\t\t\t\t\t\t} else {\r\n\t\t\t\t\t\t\terrorMessage = error.message\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t}\r\n\r\n\t\t\t\t\t// 使用可靠的错误提示方法\r\n\t\t\t\t\tthis.showErrorMessage(errorMessage, true) // 使用modal显示\r\n\r\n\t\t\t\t\t// 刷新验证码\r\n\t\t\t\t\tif (this.captchaEnabled) {\r\n\t\t\t\t\t\tawait this.refreshCaptcha()\r\n\t\t\t\t\t}\r\n\t\t\t\t} finally {\r\n\t\t\t\t\tthis.passwordLoading = false\r\n\t\t\t\t}\r\n\t\t\t},\r\n\r\n\t\t\t// 发送短信验证码\r\n\t\t\tasync sendSmsCode() {\r\n\t\t\t\tif (!this.phoneForm.phone) {\r\n\t\t\t\t\tuni.showToast({\r\n\t\t\t\t\t\ttitle: '请输入手机号',\r\n\t\t\t\t\t\ticon: 'none'\r\n\t\t\t\t\t})\r\n\t\t\t\t\treturn\r\n\t\t\t\t}\r\n\r\n\t\t\t\tif (!/^1[3-9]\\d{9}$/.test(this.phoneForm.phone)) {\r\n\t\t\t\t\tuni.showToast({\r\n\t\t\t\t\t\ttitle: '请输入正确的手机号',\r\n\t\t\t\t\t\ticon: 'none'\r\n\t\t\t\t\t})\r\n\t\t\t\t\treturn\r\n\t\t\t\t}\r\n\r\n\t\t\t\ttry {\r\n\t\t\t\t\tconst res = await sendPhoneCode(this.phoneForm.phone)\r\n\t\t\t\t\tif (res.code === 200) {\r\n\t\t\t\t\t\tuni.showToast({\r\n\t\t\t\t\t\t\ttitle: '发送成功',\r\n\t\t\t\t\t\t\ticon: 'success'\r\n\t\t\t\t\t\t})\r\n\t\t\t\t\t\tthis.startSmsCountdown()\r\n\t\t\t\t\t} else {\r\n\t\t\t\t\t\tuni.showToast({\r\n\t\t\t\t\t\t\ttitle: res.msg || '发送失败',\r\n\t\t\t\t\t\t\ticon: 'none'\r\n\t\t\t\t\t\t})\r\n\t\t\t\t\t}\r\n\t\t\t\t} catch (error) {\r\n\t\t\t\t\tconsole.error('发送短信失败:', error)\r\n\t\t\t\t\tuni.showToast({\r\n\t\t\t\t\t\ttitle: '发送失败，请稍后再试',\r\n\t\t\t\t\t\ticon: 'none'\r\n\t\t\t\t\t})\r\n\t\t\t\t}\r\n\t\t\t},\r\n\r\n\t\t\t// 开始短信倒计时\r\n\t\t\tstartSmsCountdown() {\r\n\t\t\t\tthis.smsCountdown = 60\r\n\t\t\t\tthis.smsDisabled = true\r\n\t\t\t\tthis.smsButtonText = `${this.smsCountdown}s`\r\n\r\n\t\t\t\tconst timer = setInterval(() => {\r\n\t\t\t\t\tthis.smsCountdown--\r\n\t\t\t\t\tthis.smsButtonText = `${this.smsCountdown}s`\r\n\r\n\t\t\t\t\tif (this.smsCountdown <= 0) {\r\n\t\t\t\t\t\tclearInterval(timer)\r\n\t\t\t\t\t\tthis.smsDisabled = false\r\n\t\t\t\t\t\tthis.smsButtonText = '获取验证码'\r\n\t\t\t\t\t}\r\n\t\t\t\t}, 1000)\r\n\t\t\t},\r\n\r\n\t\t\t// 手机登录\r\n\t\t\tasync handlePhoneLogin() {\r\n\t\t\t\tif (!this.phoneForm.phone) {\r\n\t\t\t\t\tuni.showToast({\r\n\t\t\t\t\t\ttitle: '请输入手机号',\r\n\t\t\t\t\t\ticon: 'none'\r\n\t\t\t\t\t})\r\n\t\t\t\t\treturn\r\n\t\t\t\t}\r\n\r\n\t\t\t\tif (!/^1[3-9]\\d{9}$/.test(this.phoneForm.phone)) {\r\n\t\t\t\t\tuni.showToast({\r\n\t\t\t\t\t\ttitle: '请输入正确的手机号',\r\n\t\t\t\t\t\ticon: 'none'\r\n\t\t\t\t\t})\r\n\t\t\t\t\treturn\r\n\t\t\t\t}\r\n\r\n\t\t\t\tif (!this.phoneForm.phoneCode) {\r\n\t\t\t\t\tuni.showToast({\r\n\t\t\t\t\t\ttitle: '请输入短信验证码',\r\n\t\t\t\t\t\ticon: 'none'\r\n\t\t\t\t\t})\r\n\t\t\t\t\treturn\r\n\t\t\t\t}\r\n\r\n\t\t\t\tthis.phoneLoading = true\r\n\r\n\t\t\t\ttry {\r\n\t\t\t\t\tconst res = await phoneLogin(this.phoneForm.phone, this.phoneForm.phoneCode)\r\n\r\n\t\t\t\t\tif (res.code === 200) {\r\n\t\t\t\t\t\t// 保存登录信息\r\n\t\t\t\t\t\ttry {\r\n\r\n\t\t\t\t\t\t\t// 安全地获取token\r\n\t\t\t\t\t\t\tlet token = null\r\n\t\t\t\t\t\t\tif (res.token) {\r\n\t\t\t\t\t\t\t\ttoken = res.token\r\n\t\t\t\t\t\t\t} else if (res.data && res.data.token) {\r\n\t\t\t\t\t\t\t\ttoken = res.data.token\r\n\t\t\t\t\t\t\t} else if (res.access_token) {\r\n\t\t\t\t\t\t\t\ttoken = res.access_token\r\n\t\t\t\t\t\t\t}\r\n\r\n\r\n\t\t\t\t\t\t\tif (!token) {\r\n\t\t\t\t\t\t\t\tthrow new Error('响应中未找到有效的token')\r\n\t\t\t\t\t\t\t}\r\n\r\n\t\t\t\t\t\t\t// 保存token\r\n\t\t\t\t\t\t\tsetToken(token)\r\n\r\n\t\t\t\t\t\t\t// 安全地构造用户信息对象\r\n\t\t\t\t\t\t\tconst userInfo = {\r\n\t\t\t\t\t\t\t\tusername: (res.data && res.data.username) ||\r\n\t\t\t\t\t\t\t\t\t(res.user && res.user.username) ||\r\n\t\t\t\t\t\t\t\t\tthis.phoneForm.phone ||\r\n\t\t\t\t\t\t\t\t\t'手机用户',\r\n\t\t\t\t\t\t\t\tuserId: (res.data && res.data.userId) ||\r\n\t\t\t\t\t\t\t\t\t(res.data && res.data.id) ||\r\n\t\t\t\t\t\t\t\t\t(res.user && res.user.id) ||\r\n\t\t\t\t\t\t\t\t\t'phone_user_' + Date.now(),\r\n\t\t\t\t\t\t\t\tavatar: (res.data && res.data.avatar) ||\r\n\t\t\t\t\t\t\t\t\t(res.user && res.user.avatar) ||\r\n\t\t\t\t\t\t\t\t\t'',\r\n\t\t\t\t\t\t\t\troles: (res.data && res.data.roles) ||\r\n\t\t\t\t\t\t\t\t\t(res.user && res.user.roles) || ['user'],\r\n\t\t\t\t\t\t\t\tphone: this.phoneForm.phone\r\n\t\t\t\t\t\t\t}\r\n\r\n\t\t\t\t\t\t\tsetUserInfo(userInfo)\r\n\r\n\t\t\t\t\t\t\t// 验证保存结果\r\n\t\t\t\t\t\t\tconst savedToken = uni.getStorageSync('user_token')\r\n\t\t\t\t\t\t\tconst savedUserInfo = uni.getStorageSync('user_info')\r\n\r\n\t\t\t\t\t\t\tif (!savedToken) {\r\n\t\t\t\t\t\t\t\tthrow new Error('Token保存失败')\r\n\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\tif (!savedUserInfo) {\r\n\t\t\t\t\t\t\t\tthrow new Error('用户信息保存失败')\r\n\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t} catch (error) {\r\n\t\t\t\t\t\t\tconsole.error('手机登录 - 保存登录信息失败:', error)\r\n\t\t\t\t\t\t\tuni.showToast({\r\n\t\t\t\t\t\t\t\ttitle: '登录状态保存失败: ' + error.message,\r\n\t\t\t\t\t\t\t\ticon: 'none',\r\n\t\t\t\t\t\t\t\tduration: 3000\r\n\t\t\t\t\t\t\t})\r\n\t\t\t\t\t\t\treturn\r\n\t\t\t\t\t\t}\r\n\r\n\t\t\t\t\t\tuni.showToast({\r\n\t\t\t\t\t\t\ttitle: '登录成功',\r\n\t\t\t\t\t\t\ticon: 'success',\r\n\t\t\t\t\t\t\tduration: 1000\r\n\t\t\t\t\t\t})\r\n\r\n\t\t\t\t\t\t// 跳转到首页\r\n\t\t\t\t\t\tsetTimeout(() => {\r\n\t\t\t\t\t\t\t// 再次验证登录状态\r\n\t\t\t\t\t\t\tconst finalToken = uni.getStorageSync('user_token')\r\n\t\t\t\t\t\t\tconst finalUserInfo = uni.getStorageSync('user_info')\r\n\r\n\t\t\t\t\t\t\tif (!finalToken || !finalUserInfo) {\r\n\t\t\t\t\t\t\t\tconsole.error('手机登录 - 登录信息验证失败，无法跳转')\r\n\t\t\t\t\t\t\t\tuni.showToast({\r\n\t\t\t\t\t\t\t\t\ttitle: '登录状态异常，请重试',\r\n\t\t\t\t\t\t\t\t\ticon: 'none'\r\n\t\t\t\t\t\t\t\t})\r\n\t\t\t\t\t\t\t\treturn\r\n\t\t\t\t\t\t\t}\r\n\r\n\t\t\t\t\t\t\t// 临时禁用路由守卫，避免跳转时被拦截\r\n\t\t\t\t\t\t\tdisableGuard()\r\n\r\n\t\t\t\t\t\t\tuni.reLaunch({\r\n\t\t\t\t\t\t\t\turl: '/pages/index/index',\r\n\t\t\t\t\t\t\t\tsuccess: () => {},\r\n\t\t\t\t\t\t\t\tfail: (error) => {\r\n\t\t\t\t\t\t\t\t\tconsole.error('手机登录 - 跳转首页失败:', error)\r\n\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t})\r\n\t\t\t\t\t\t}, 1200)\r\n\t\t\t\t\t} else {\r\n\t\t\t\t\t\tconsole.error('手机登录失败:', res)\r\n\r\n\t\t\t\t\t\t// 提取错误信息\r\n\t\t\t\t\t\tlet errorMessage = '登录失败'\r\n\t\t\t\t\t\tif (res.msg) {\r\n\t\t\t\t\t\t\terrorMessage = res.msg\r\n\t\t\t\t\t\t} else if (res.message) {\r\n\t\t\t\t\t\t\terrorMessage = res.message\r\n\t\t\t\t\t\t} else if (res.error) {\r\n\t\t\t\t\t\t\terrorMessage = res.error\r\n\t\t\t\t\t\t} else if (res.data && res.data.message) {\r\n\t\t\t\t\t\t\terrorMessage = res.data.message\r\n\t\t\t\t\t\t}\r\n\r\n\t\t\t\t\t\tuni.showToast({\r\n\t\t\t\t\t\t\ttitle: errorMessage,\r\n\t\t\t\t\t\t\ticon: 'none',\r\n\t\t\t\t\t\t\tduration: 3000\r\n\t\t\t\t\t\t})\r\n\t\t\t\t\t}\r\n\t\t\t\t} catch (error) {\r\n\t\t\t\t\tconsole.error('手机登录失败:', error)\r\n\r\n\t\t\t\t\tlet errorMessage = '登录失败，请稍后再试'\r\n\r\n\t\t\t\t\t// 根据错误类型提供更具体的错误信息\r\n\t\t\t\t\tif (error.message) {\r\n\t\t\t\t\t\tif (error.message.includes('请先配置正确的 API 地址')) {\r\n\t\t\t\t\t\t\terrorMessage = 'API地址未配置，请联系管理员'\r\n\t\t\t\t\t\t} else if (error.message.includes('网络')) {\r\n\t\t\t\t\t\t\terrorMessage = '网络连接失败，请检查网络'\r\n\t\t\t\t\t\t} else if (error.message.includes('超时')) {\r\n\t\t\t\t\t\t\terrorMessage = '请求超时，请重试'\r\n\t\t\t\t\t\t} else {\r\n\t\t\t\t\t\t\terrorMessage = error.message\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t}\r\n\r\n\t\t\t\t\tuni.showToast({\r\n\t\t\t\t\t\ttitle: errorMessage,\r\n\t\t\t\t\t\ticon: 'none',\r\n\t\t\t\t\t\tduration: 3000\r\n\t\t\t\t\t})\r\n\t\t\t\t} finally {\r\n\t\t\t\t\tthis.phoneLoading = false\r\n\t\t\t\t}\r\n\t\t\t},\r\n\r\n\t\t\t// 前往注册页面\r\n\t\t\tgoToRegister() {\r\n\t\t\t\tuni.navigateTo({\r\n\t\t\t\t\turl: '/pages/register/register'\r\n\t\t\t\t})\r\n\t\t\t}\r\n\t\t}\r\n\t}\r\n</script>\r\n\r\n<style scoped>\r\n\t/* 登录容器 */\r\n\t.login-container {\r\n\t\tmin-height: 100vh;\r\n\t\tbackground: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\r\n\t\tdisplay: flex;\r\n\t\talign-items: center;\r\n\t\tjustify-content: center;\r\n\t\tpadding: 40rpx;\r\n\t\tposition: relative;\r\n\t\toverflow: hidden;\r\n\t}\r\n\r\n\t/* 背景装饰 */\r\n\t.bg-decoration {\r\n\t\tposition: absolute;\r\n\t\ttop: 0;\r\n\t\tleft: 0;\r\n\t\tright: 0;\r\n\t\tbottom: 0;\r\n\t\tpointer-events: none;\r\n\t}\r\n\r\n\t.circle {\r\n\t\tposition: absolute;\r\n\t\tborder-radius: 50%;\r\n\t\tbackground: rgba(255, 255, 255, 0.1);\r\n\t\tanimation: float 6s ease-in-out infinite;\r\n\t}\r\n\r\n\t.circle-1 {\r\n\t\twidth: 200rpx;\r\n\t\theight: 200rpx;\r\n\t\ttop: 10%;\r\n\t\tleft: 10%;\r\n\t\tanimation-delay: 0s;\r\n\t}\r\n\r\n\t.circle-2 {\r\n\t\twidth: 150rpx;\r\n\t\theight: 150rpx;\r\n\t\ttop: 60%;\r\n\t\tright: 15%;\r\n\t\tanimation-delay: 2s;\r\n\t}\r\n\r\n\t.circle-3 {\r\n\t\twidth: 100rpx;\r\n\t\theight: 100rpx;\r\n\t\tbottom: 20%;\r\n\t\tleft: 20%;\r\n\t\tanimation-delay: 4s;\r\n\t}\r\n\r\n\t@keyframes float {\r\n\r\n\t\t0%,\r\n\t\t100% {\r\n\t\t\ttransform: translateY(0px) rotate(0deg);\r\n\t\t}\r\n\r\n\t\t50% {\r\n\t\t\ttransform: translateY(-20px) rotate(180deg);\r\n\t\t}\r\n\t}\r\n\r\n\t/* 登录卡片 */\r\n\t.login-card {\r\n\t\tbackground: rgba(255, 255, 255, 0.95);\r\n\t\tbackdrop-filter: blur(10px);\r\n\t\tborder-radius: 24rpx;\r\n\t\tpadding: 60rpx 40rpx;\r\n\t\twidth: 100%;\r\n\t\tmax-width: 600rpx;\r\n\t\tbox-shadow: 0 20rpx 60rpx rgba(0, 0, 0, 0.1);\r\n\t\tposition: relative;\r\n\t\tz-index: 1;\r\n\t}\r\n\r\n\t.fade-in {\r\n\t\tanimation: fadeInUp 0.8s ease-out;\r\n\t}\r\n\r\n\t@keyframes fadeInUp {\r\n\t\tfrom {\r\n\t\t\topacity: 0;\r\n\t\t\ttransform: translateY(50rpx);\r\n\t\t}\r\n\r\n\t\tto {\r\n\t\t\topacity: 1;\r\n\t\t\ttransform: translateY(0);\r\n\t\t}\r\n\t}\r\n\r\n\t/* 登录头部 */\r\n\t.login-header {\r\n\t\ttext-align: center;\r\n\t\tmargin-bottom: 60rpx;\r\n\t}\r\n\r\n\t.logo {\r\n\t\tfont-size: 80rpx;\r\n\t\tmargin-bottom: 20rpx;\r\n\t}\r\n\r\n\t.title {\r\n\t\tfont-size: 48rpx;\r\n\t\tfont-weight: bold;\r\n\t\tcolor: #333333;\r\n\t\tmargin-bottom: 10rpx;\r\n\t}\r\n\r\n\t.subtitle {\r\n\t\tfont-size: 28rpx;\r\n\t\tcolor: #666666;\r\n\t}\r\n\r\n\t/* 标签页 */\r\n\t.tab-container {\r\n\t\tdisplay: flex;\r\n\t\tbackground: #f5f5f5;\r\n\t\tborder-radius: 12rpx;\r\n\t\tpadding: 8rpx;\r\n\t\tmargin-bottom: 40rpx;\r\n\t}\r\n\r\n\t.tab-item {\r\n\t\tflex: 1;\r\n\t\ttext-align: center;\r\n\t\tpadding: 20rpx;\r\n\t\tborder-radius: 8rpx;\r\n\t\tfont-size: 28rpx;\r\n\t\tcolor: #666666;\r\n\t\ttransition: all 0.3s ease;\r\n\t\tcursor: pointer;\r\n\t}\r\n\r\n\t.tab-item.active {\r\n\t\tbackground: #ffffff;\r\n\t\tcolor: #667eea;\r\n\t\tfont-weight: bold;\r\n\t\tbox-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.1);\r\n\t}\r\n\r\n\t/* 表单容器 */\r\n\t.form-container {\r\n\t\tmargin-bottom: 40rpx;\r\n\t}\r\n\r\n\t/* 输入组 */\r\n\t.input-group {\r\n\t\tdisplay: flex;\r\n\t\talign-items: center;\r\n\t\tbackground: #f8f9fa;\r\n\t\tborder-radius: 16rpx;\r\n\t\tpadding: 0 20rpx;\r\n\t\tmargin-bottom: 30rpx;\r\n\t\tborder: 2rpx solid transparent;\r\n\t\ttransition: all 0.3s ease;\r\n\t}\r\n\r\n\t.input-group:focus-within {\r\n\t\tborder-color: #667eea;\r\n\t\tbackground: #ffffff;\r\n\t\tbox-shadow: 0 0 0 4rpx rgba(102, 126, 234, 0.1);\r\n\t}\r\n\r\n\t.input-icon {\r\n\t\tfont-size: 32rpx;\r\n\t\tmargin-right: 20rpx;\r\n\t\tcolor: #999999;\r\n\t}\r\n\r\n\t.input-field {\r\n\t\tflex: 1;\r\n\t\theight: 80rpx;\r\n\t\tfont-size: 28rpx;\r\n\t\tcolor: #333333;\r\n\t\tbackground: transparent;\r\n\t\tborder: none;\r\n\t}\r\n\r\n\t.input-suffix {\r\n\t\tfont-size: 32rpx;\r\n\t\tcolor: #999999;\r\n\t\tcursor: pointer;\r\n\t\tpadding: 10rpx;\r\n\t}\r\n\r\n\t/* 验证码相关 */\r\n\t.captcha-group {\r\n\t\tpadding-right: 0;\r\n\t}\r\n\r\n\t.captcha-input {\r\n\t\tflex: 1;\r\n\t\tmargin-right: 20rpx;\r\n\t}\r\n\r\n\t.captcha-image {\r\n\t\twidth: 160rpx;\r\n\t\theight: 80rpx;\r\n\t\tborder-radius: 8rpx;\r\n\t\toverflow: hidden;\r\n\t\tcursor: pointer;\r\n\t\tdisplay: flex;\r\n\t\talign-items: center;\r\n\t\tjustify-content: center;\r\n\t\tbackground: #e9ecef;\r\n\t}\r\n\r\n\t.captcha-img {\r\n\t\twidth: 100%;\r\n\t\theight: 100%;\r\n\t}\r\n\r\n\t.captcha-placeholder {\r\n\t\tfont-size: 24rpx;\r\n\t\tcolor: #999999;\r\n\t}\r\n\r\n\t/* 短信验证码 */\r\n\t.sms-group {\r\n\t\tpadding-right: 0;\r\n\t}\r\n\r\n\t.sms-input {\r\n\t\tflex: 1;\r\n\t\tmargin-right: 20rpx;\r\n\t}\r\n\r\n\t.sms-btn {\r\n\t\tpadding: 20rpx 30rpx;\r\n\t\tbackground: #667eea;\r\n\t\tcolor: #ffffff;\r\n\t\tborder: none;\r\n\t\tborder-radius: 12rpx;\r\n\t\tfont-size: 24rpx;\r\n\t\tcursor: pointer;\r\n\t\ttransition: all 0.3s ease;\r\n\t}\r\n\r\n\t.sms-btn:disabled {\r\n\t\tbackground: #c0c4cc;\r\n\t\tcursor: not-allowed;\r\n\t}\r\n\r\n\t/* 复选框 */\r\n\t.checkbox-group {\r\n\t\tdisplay: flex;\r\n\t\talign-items: center;\r\n\t\tmargin-bottom: 40rpx;\r\n\t}\r\n\r\n\t.checkbox {\r\n\t\twidth: 36rpx;\r\n\t\theight: 36rpx;\r\n\t\tborder: 2rpx solid #ddd;\r\n\t\tborder-radius: 6rpx;\r\n\t\tdisplay: flex;\r\n\t\talign-items: center;\r\n\t\tjustify-content: center;\r\n\t\tmargin-right: 15rpx;\r\n\t\tcursor: pointer;\r\n\t\ttransition: all 0.3s ease;\r\n\t}\r\n\r\n\t.checkbox.checked {\r\n\t\tbackground: #667eea;\r\n\t\tborder-color: #667eea;\r\n\t}\r\n\r\n\t.checkbox-icon {\r\n\t\tcolor: #ffffff;\r\n\t\tfont-size: 20rpx;\r\n\t\tfont-weight: bold;\r\n\t}\r\n\r\n\t.checkbox-label {\r\n\t\tfont-size: 26rpx;\r\n\t\tcolor: #666666;\r\n\t}\r\n\r\n\t/* 登录按钮 */\r\n\t.login-btn {\r\n\t\twidth: 100%;\r\n\t\theight: 88rpx;\r\n\t\tbackground: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\r\n\t\tcolor: #ffffff;\r\n\t\tborder: none;\r\n\t\tborder-radius: 44rpx;\r\n\t\tfont-size: 32rpx;\r\n\t\tfont-weight: bold;\r\n\t\tdisplay: flex;\r\n\t\talign-items: center;\r\n\t\tjustify-content: center;\r\n\t\tcursor: pointer;\r\n\t\ttransition: all 0.3s ease;\r\n\t\tbox-shadow: 0 8rpx 24rpx rgba(102, 126, 234, 0.3);\r\n\t}\r\n\r\n\t.login-btn:hover {\r\n\t\ttransform: translateY(-2rpx);\r\n\t\tbox-shadow: 0 12rpx 32rpx rgba(102, 126, 234, 0.4);\r\n\t}\r\n\r\n\t.login-btn.loading,\r\n\t.login-btn:disabled {\r\n\t\tbackground: #c0c4cc;\r\n\t\tcursor: not-allowed;\r\n\t\ttransform: none;\r\n\t\tbox-shadow: 0 8rpx 24rpx rgba(192, 196, 204, 0.3);\r\n\t}\r\n\r\n\t.loading-icon {\r\n\t\tmargin-right: 15rpx;\r\n\t\tanimation: spin 1s linear infinite;\r\n\t}\r\n\r\n\t@keyframes spin {\r\n\t\tfrom {\r\n\t\t\ttransform: rotate(0deg);\r\n\t\t}\r\n\r\n\t\tto {\r\n\t\t\ttransform: rotate(360deg);\r\n\t\t}\r\n\t}\r\n\r\n\t/* 底部 */\r\n\t.login-footer {\r\n\t\ttext-align: center;\r\n\t\tmargin-top: 40rpx;\r\n\t}\r\n\r\n\t.footer-link {\r\n\t\tcolor: #667eea;\r\n\t\tfont-size: 26rpx;\r\n\t\tcursor: pointer;\r\n\t\ttransition: all 0.3s ease;\r\n\t}\r\n\r\n\t.footer-link:hover {\r\n\t\tcolor: #764ba2;\r\n\t}\r\n\r\n\t/* 响应式设计 */\r\n\t@media screen and (max-width: 750rpx) {\r\n\t\t.login-card {\r\n\t\t\tpadding: 40rpx 30rpx;\r\n\t\t}\r\n\r\n\t\t.title {\r\n\t\t\tfont-size: 42rpx;\r\n\t\t}\r\n\r\n\t\t.subtitle {\r\n\t\t\tfont-size: 26rpx;\r\n\t\t}\r\n\t}\r\n</style>", "import mod from \"-!../../../../../app/HBuilderX/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--6-oneOf-1-0!../../../../../app/HBuilderX/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--6-oneOf-1-1!../../../../../app/HBuilderX/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../app/HBuilderX/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--6-oneOf-1-2!../../../../../app/HBuilderX/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--6-oneOf-1-3!../../../../../app/HBuilderX/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../app/HBuilderX/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./login.vue?vue&type=style&index=0&id=b237504c&scoped=true&lang=css&\"; export default mod; export * from \"-!../../../../../app/HBuilderX/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--6-oneOf-1-0!../../../../../app/HBuilderX/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--6-oneOf-1-1!../../../../../app/HBuilderX/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../app/HBuilderX/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--6-oneOf-1-2!../../../../../app/HBuilderX/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--6-oneOf-1-3!../../../../../app/HBuilderX/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../app/HBuilderX/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./login.vue?vue&type=style&index=0&id=b237504c&scoped=true&lang=css&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1755740756679\n      var cssReload = require(\"D:/app/HBuilderX/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}