{"version": 3, "sources": ["uni-app:///main.js", null, "webpack:///D:/projects/HBuilder/fq_sign_uiapp/pages/register/register.vue?21b4", "webpack:///D:/projects/HBuilder/fq_sign_uiapp/pages/register/register.vue?cf5b", "webpack:///D:/projects/HBuilder/fq_sign_uiapp/pages/register/register.vue?6d5f", "uni-app:///pages/register/register.vue", "webpack:///D:/projects/HBuilder/fq_sign_uiapp/pages/register/register.vue?d3da", "webpack:///D:/projects/HBuilder/fq_sign_uiapp/pages/register/register.vue?9ee3"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page", "data", "registerForm", "username", "phone", "phoneCode", "password", "confirmPassword", "code", "uuid", "agreement", "registerLoading", "showPassword", "showConfirmPassword", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "codeUrl", "smsDisabled", "smsButtonText", "smsCountdown", "showAgreementModal", "showPrivacyModal", "showWechatPrivacyModal", "computed", "passwordStrength", "passwordStrengthText", "canRegister", "onLoad", "methods", "showErrorMessage", "console", "uni", "title", "content", "showCancel", "confirmText", "setTimeout", "icon", "duration", "mask", "initPage", "togglePassword", "toggleConfirmPassword", "toggleAgreement", "refreshCaptcha", "res", "sendSmsCode", "startSmsCountdown", "clearInterval", "handleRegister", "success", "errorMsg", "errorMessage", "validateForm", "showAgreement", "closeAgreementModal", "showPrivacy", "closePrivacyModal", "goToLogin", "acceptPrivacyConsent", "rejectPrivacyConsent", "cancelText"], "mappings": ";;;;;;;;;;;;;AAAA;AAGA;AACA;AAHA;AACAA,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG1DC,UAAU,CAACC,iBAAI,CAAC,C;;;;;;;;;;;;;ACLhB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAiI;AACjI;AAC4D;AACL;AACqC;;;AAG5F;AACoL;AACpL,gBAAgB,2LAAU;AAC1B,EAAE,8EAAM;AACR,EAAE,+FAAM;AACR,EAAE,wGAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,mGAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;ACRA;AAAA;AAAA;AAAA;AAA0rB,CAAgB,wrBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;ACgM9sB;AAKA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;eAIA;EACAC;IACA;MACA;MACAC;QACAC;QACAC;QACAC;QACAC;QACAC;QACAC;QACAC;QACAC;MACA;MAEA;MACAC;MACAC;MACAC;MACAC;MACAC;MAEA;MACAC;MACAC;MACAC;MAEA;MACAC;MACAC;MACAC;IACA;EACA;EAEAC;IACA;IACAC;MACA;MACA;MAEA;MACA;MACA;MACA;MACA;MACA;MACA;MAEA;IACA;IAEA;IACAC;MACA;QACA;QACA;UACA;QACA;UACA;QACA;UACA;QACA;UACA;MAAA;IAEA;IAEA;IACAC;MACA,qCACA,2BACA,+BACA,8BACA,qCACA,gCACA;IACA;EACA;EAEAC;IACA;;IAEA;EAKA;EAEAC;IACA;IACAC;MAAA;MACAC;MAEA;QACAC;UACAC;UACAC;UACAC;UACAC;QACA;MACA;QACA;QACAJ;QACAK;UACAL;YACAC;YACAK;YACAC;YACAC;UACA;QACA;MACA;IACA;IAEA;IACAC;MAAA;MAAA;QAAA;UAAA;YAAA;cAAA;gBAAA;gBAAA,OAEA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IACA;IAEA;IACAC;MACA;IACA;IAEA;IACAC;MACA;IACA;IAEA;IACAC;MACA;IACA;IAEA;IACAC;MAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBAAA;gBAAA;gBAAA,OAEA;cAAA;gBAAAC;gBACA;kBACA;kBACA;oBACA;oBACA;kBACA;gBACA;gBAAA;gBAAA;cAAA;gBAAA;gBAAA;gBAEAf;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IAEA;IAEA;IACAgB;MAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBAAA,IACA;kBAAA;kBAAA;gBAAA;gBACAf;kBACAC;kBACAK;gBACA;gBAAA;cAAA;gBAAA,IAIA;kBAAA;kBAAA;gBAAA;gBACAN;kBACAC;kBACAK;gBACA;gBAAA;cAAA;gBAAA;gBAAA;gBAAA,OAKA;cAAA;gBAAAQ;gBACA;kBACAd;oBACAC;oBACAK;kBACA;kBACA;gBACA;kBACAN;oBACAC;oBACAK;kBACA;gBACA;gBAAA;gBAAA;cAAA;gBAAA;gBAAA;gBAEAP;gBACAC;kBACAC;kBACAK;gBACA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IAEA;IAEA;IACAU;MAAA;MACA;MACA;MACA;MAEA;QACA;QACA;QAEA;UACAC;UACA;UACA;QACA;MACA;IACA;IAEA;IACAC;MAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBAAA,IAEA;kBAAA;kBAAA;gBAAA;gBAAA;cAAA;gBAIA;gBAAA;gBAAA;gBAAA,OAGA;kBACA7C;kBACAC;kBACAC;kBACAC;kBACAE;kBACAC;gBACA;cAAA;gBAPAmC;gBAAA,MASAA;kBAAA;kBAAA;gBAAA;gBACAd;kBACAC;kBACAC;kBACAC;kBACAgB;oBACA;kBACA;gBACA;gBAAA;gBAAA;cAAA;gBAEA;gBACAC;gBACArB;;gBAEA;gBACA;;gBAEA;gBAAA,KACA;kBAAA;kBAAA;gBAAA;gBAAA;gBAAA,OACA;cAAA;gBAAA;gBAAA;cAAA;gBAAA;gBAAA;gBAIAA;gBACA;gBACAsB,6BAEA;gBACA;kBACAA;gBACA;gBAEAtB;;gBAEA;gBACA;;gBAEA;gBAAA,KACA;kBAAA;kBAAA;gBAAA;gBAAA;gBAAA,OACA;cAAA;gBAAA;gBAGA;gBAAA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IAEA;IAEA;IACAuB;MACA;QACAtB;UACAC;UACAK;QACA;QACA;MACA;MAEA;QACAN;UACAC;UACAK;QACA;QACA;MACA;MAEA;QACAN;UACAC;UACAK;QACA;QACA;MACA;MAEA;QACAN;UACAC;UACAK;QACA;QACA;MACA;MAEA;QACAN;UACAC;UACAK;QACA;QACA;MACA;MAEA;QACAN;UACAC;UACAK;QACA;QACA;MACA;MAEA;QACAN;UACAC;UACAK;QACA;QACA;MACA;MAEA;QACAN;UACAC;UACAK;QACA;QACA;MACA;MAEA;QACAN;UACAC;UACAK;QACA;QACA;MACA;MAEA;QACAN;UACAC;UACAK;QACA;QACA;MACA;MAEA;QACAN;UACAC;UACAK;QACA;QACA;MACA;MAEA;IACA;IAEA;IACAiB;MACA;IACA;IAEA;IACAC;MACA;IACA;IAEA;IACAC;MACA;IACA;IAEA;IACAC;MACA;IACA;IAEA;IACAC;MACA3B;IACA;IAEA;IACA4B;MACA;MACA;MACA;IACA;IAEA;IACAC;MAAA;MACA7B;QACAC;QACAC;QACAE;QACA0B;QACAX;UACA;YACA;YACA;UACA;UACA;QACA;MACA;IACA;EACA;AACA;AAAA,2B;;;;;;;;;;;;;AC1mBA;AAAA;AAAA;AAAA;AAAugC,CAAgB,i+BAAG,EAAC,C;;;;;;;;;;;ACA3hC;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "pages/register/register.js", "sourcesContent": ["import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;\nimport Vue from 'vue'\nimport Page from './pages/register/register.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./register.vue?vue&type=template&id=891c2434&scoped=true&\"\nvar renderjs\nimport script from \"./register.vue?vue&type=script&lang=js&\"\nexport * from \"./register.vue?vue&type=script&lang=js&\"\nimport style0 from \"./register.vue?vue&type=style&index=0&id=891c2434&scoped=true&lang=css&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../../app/HBuilderX/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"891c2434\",\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"pages/register/register.vue\"\nexport default component.exports", "export * from \"-!../../../../../app/HBuilderX/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../../../app/HBuilderX/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--17-0!../../../../../app/HBuilderX/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!../../../../../app/HBuilderX/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!../../../../../app/HBuilderX/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../app/HBuilderX/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./register.vue?vue&type=template&id=891c2434&scoped=true&\"", "var components\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!../../../../../app/HBuilderX/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../app/HBuilderX/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../app/HBuilderX/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../app/HBuilderX/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../app/HBuilderX/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./register.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../../../app/HBuilderX/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../app/HBuilderX/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../app/HBuilderX/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../app/HBuilderX/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../app/HBuilderX/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./register.vue?vue&type=script&lang=js&\"", "<template>\r\n\t<view class=\"register-container\">\r\n\t\t<!-- 背景装饰 -->\r\n\t\t<view class=\"bg-decoration\">\r\n\t\t\t<view class=\"circle circle-1\"></view>\r\n\t\t\t<view class=\"circle circle-2\"></view>\r\n\t\t\t<view class=\"circle circle-3\"></view>\r\n\t\t</view>\r\n\r\n\t\t<!-- 注册卡片 -->\r\n\t\t<view class=\"register-card fade-in\">\r\n\t\t\t<!-- 头部 -->\r\n\t\t\t<view class=\"register-header\">\r\n\t\t\t\t<view class=\"logo\">📝</view>\r\n\t\t\t\t<view class=\"title\">创建新账户</view>\r\n\t\t\t\t<view class=\"subtitle\">请填写以下信息完成注册</view>\r\n\t\t\t</view>\r\n\r\n\t\t\t<!-- 注册表单 -->\r\n\t\t\t<view class=\"form-container\">\r\n\t\t\t\t<!-- 用户名 -->\r\n\t\t\t\t<view class=\"input-group\">\r\n\t\t\t\t\t<view class=\"input-icon\">👤</view>\r\n\t\t\t\t\t<input v-model=\"registerForm.username\" placeholder=\"请输入用户名\" class=\"input-field\" />\r\n\t\t\t\t</view>\r\n\r\n\t\t\t\t<!-- 手机号 -->\r\n\t\t\t\t<view class=\"input-group\">\r\n\t\t\t\t\t<view class=\"input-icon\">📱</view>\r\n\t\t\t\t\t<input v-model=\"registerForm.phone\" placeholder=\"请输入手机号\" class=\"input-field\" type=\"number\" />\r\n\t\t\t\t</view>\r\n\r\n\t\t\t\t<!-- 短信验证码 -->\r\n\t\t\t\t<view class=\"input-group sms-group\">\r\n\t\t\t\t\t<view class=\"input-icon\">💬</view>\r\n\t\t\t\t\t<input v-model=\"registerForm.phoneCode\" placeholder=\"请输入短信验证码\" class=\"input-field sms-input\" />\r\n\t\t\t\t\t<button class=\"sms-btn\" :disabled=\"smsDisabled\" @click=\"sendSmsCode\">\r\n\t\t\t\t\t\t{{ smsButtonText }}\r\n\t\t\t\t\t</button>\r\n\t\t\t\t</view>\r\n\r\n\t\t\t\t<!-- 密码 -->\r\n\t\t\t\t<view class=\"input-group\">\r\n\t\t\t\t\t<view class=\"input-icon\">🔒</view>\r\n\t\t\t\t\t<input v-model=\"registerForm.password\" placeholder=\"请输入密码\" class=\"input-field\"\r\n\t\t\t\t\t\t:password=\"!showPassword\" />\r\n\t\t\t\t\t<view class=\"input-suffix\" @click=\"togglePassword\">\r\n\t\t\t\t\t\t{{ showPassword ? '🙈' : '👁️' }}\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\r\n\t\t\t\t<!-- 确认密码 -->\r\n\t\t\t\t<view class=\"input-group\">\r\n\t\t\t\t\t<view class=\"input-icon\">🔐</view>\r\n\t\t\t\t\t<input v-model=\"registerForm.confirmPassword\" placeholder=\"请确认密码\" class=\"input-field\"\r\n\t\t\t\t\t\t:password=\"!showConfirmPassword\" />\r\n\t\t\t\t\t<view class=\"input-suffix\" @click=\"toggleConfirmPassword\">\r\n\t\t\t\t\t\t{{ showConfirmPassword ? '🙈' : '👁️' }}\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\r\n\t\t\t\t<!-- 图形验证码 -->\r\n\t\t\t\t<view v-if=\"captchaEnabled\" class=\"input-group captcha-group\">\r\n\t\t\t\t\t<view class=\"input-icon\">🔢</view>\r\n\t\t\t\t\t<input v-model=\"registerForm.code\" placeholder=\"请输入验证码\" class=\"input-field captcha-input\" />\r\n\t\t\t\t\t<view class=\"captcha-image\" @click=\"refreshCaptcha\">\r\n\t\t\t\t\t\t<image v-if=\"codeUrl\" :src=\"codeUrl\" class=\"captcha-img\" mode=\"aspectFit\" />\r\n\t\t\t\t\t\t<view v-else class=\"captcha-placeholder\">点击获取</view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\r\n\t\t\t\t<!-- 密码强度提示 -->\r\n\t\t\t\t<view v-if=\"registerForm.password\" class=\"password-strength\">\r\n\t\t\t\t\t<view class=\"strength-label\">密码强度：</view>\r\n\t\t\t\t\t<view class=\"strength-bar\">\r\n\t\t\t\t\t\t<view class=\"strength-item\" :class=\"{ active: passwordStrength >= 1 }\"></view>\r\n\t\t\t\t\t\t<view class=\"strength-item\" :class=\"{ active: passwordStrength >= 2 }\"></view>\r\n\t\t\t\t\t\t<view class=\"strength-item\" :class=\"{ active: passwordStrength >= 3 }\"></view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<view class=\"strength-text\">{{ passwordStrengthText }}</view>\r\n\t\t\t\t</view>\r\n\r\n\t\t\t\t<!-- 用户协议 -->\r\n\t\t\t\t<view class=\"agreement-group\">\r\n\t\t\t\t\t<view class=\"checkbox\" :class=\"{ checked: registerForm.agreement }\" @click=\"toggleAgreement\">\r\n\t\t\t\t\t\t<view class=\"checkbox-icon\">{{ registerForm.agreement ? '✓' : '' }}</view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<text class=\"agreement-text\">\r\n\t\t\t\t\t\t我已阅读并同意\r\n\t\t\t\t\t\t<text class=\"agreement-link\" @click=\"showAgreement\">《用户协议》</text>\r\n\t\t\t\t\t\t和\r\n\t\t\t\t\t\t<text class=\"agreement-link\" @click=\"showPrivacy\">《隐私政策》</text>\r\n\t\t\t\t\t</text>\r\n\t\t\t\t</view>\r\n\r\n\t\t\t\t<!-- 注册按钮 -->\r\n\t\t\t\t<button class=\"register-btn\" :class=\"{ loading: registerLoading }\"\r\n\t\t\t\t\t:disabled=\"registerLoading || !canRegister\" @click=\"handleRegister\">\r\n\t\t\t\t\t<view v-if=\"registerLoading\" class=\"loading-icon\">⏳</view>\r\n\t\t\t\t\t{{ registerLoading ? '注册中...' : '立即注册' }}\r\n\t\t\t\t</button>\r\n\t\t\t</view>\r\n\r\n\t\t\t<!-- 底部操作 -->\r\n\t\t\t<view class=\"register-footer\">\r\n\t\t\t\t<view class=\"footer-link\" @click=\"goToLogin\">\r\n\t\t\t\t\t已有账号？立即登录\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t</view>\r\n\r\n\t\t<!-- 用户协议弹窗 -->\r\n\t\t<view v-if=\"showAgreementModal\" class=\"modal-overlay\" @click=\"closeAgreementModal\">\r\n\t\t\t<view class=\"modal-content\" @click.stop=\"\">\r\n\t\t\t\t<view class=\"modal-header\">\r\n\t\t\t\t\t<view class=\"modal-title\">用户协议</view>\r\n\t\t\t\t\t<view class=\"modal-close\" @click=\"closeAgreementModal\">×</view>\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class=\"modal-body\">\r\n\t\t\t\t\t<view class=\"agreement-content\">\r\n\t\t\t\t\t\t<text>1. 用户在使用本服务时，必须遵守相关法律法规。</text>\r\n\t\t\t\t\t\t<text>2. 用户应当保护好自己的账号和密码，不得将账号借给他人使用。</text>\r\n\t\t\t\t\t\t<text>3. 用户不得利用本服务进行任何违法违规活动。</text>\r\n\t\t\t\t\t\t<text>4. 本服务保留对用户行为进行监督和管理的权利。</text>\r\n\t\t\t\t\t\t<text>5. 如有违反本协议的行为，本服务有权终止用户的使用权限。</text>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class=\"modal-footer\">\r\n\t\t\t\t\t<button class=\"btn btn-primary\" @click=\"closeAgreementModal\">我知道了</button>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t</view>\r\n\r\n\t\t<!-- 隐私政策弹窗 -->\r\n\t\t<view v-if=\"showPrivacyModal\" class=\"modal-overlay\" @click=\"closePrivacyModal\">\r\n\t\t\t<view class=\"modal-content\" @click.stop=\"\">\r\n\t\t\t\t<view class=\"modal-header\">\r\n\t\t\t\t\t<view class=\"modal-title\">隐私政策</view>\r\n\t\t\t\t\t<view class=\"modal-close\" @click=\"closePrivacyModal\">×</view>\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class=\"modal-body\">\r\n\t\t\t\t\t<view class=\"privacy-content\">\r\n\t\t\t\t\t\t<text>1. 我们承诺保护用户的个人隐私信息。</text>\r\n\t\t\t\t\t\t<text>2. 用户的个人信息仅用于提供更好的服务体验。</text>\r\n\t\t\t\t\t\t<text>3. 我们不会向第三方泄露用户的个人信息。</text>\r\n\t\t\t\t\t\t<text>4. 用户有权查看、修改或删除自己的个人信息。</text>\r\n\t\t\t\t\t\t<text>5. 如有隐私相关问题，请联系我们的客服团队。</text>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class=\"modal-footer\">\r\n\t\t\t\t\t<button class=\"btn btn-primary\" @click=\"closePrivacyModal\">我知道了</button>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t</view>\r\n\r\n\t\t<!-- 微信小程序隐私获取同意弹窗 -->\r\n\t\t<view v-if=\"showWechatPrivacyModal\" class=\"modal-overlay privacy-consent-modal\">\r\n\t\t\t<view class=\"modal-content privacy-consent-content\" @click.stop=\"\">\r\n\t\t\t\t<view class=\"privacy-consent-header\">\r\n\t\t\t\t\t<view class=\"privacy-icon\">🔒</view>\r\n\t\t\t\t\t<view class=\"privacy-title\">隐私保护提示</view>\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class=\"privacy-consent-body\">\r\n\t\t\t\t\t<view class=\"privacy-notice\">\r\n\t\t\t\t\t\t<text class=\"privacy-text\">为了向您提供更好的服务，我们需要获取以下信息：</text>\r\n\t\t\t\t\t\t<view class=\"privacy-list\">\r\n\t\t\t\t\t\t\t<view class=\"privacy-item\">\r\n\t\t\t\t\t\t\t\t<text class=\"privacy-bullet\">•</text>\r\n\t\t\t\t\t\t\t\t<text class=\"privacy-desc\">手机号信息：用于账户安全验证</text>\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t<view class=\"privacy-item\">\r\n\t\t\t\t\t\t\t\t<text class=\"privacy-bullet\">•</text>\r\n\t\t\t\t\t\t\t\t<text class=\"privacy-desc\">网络信息：用于服务连接和数据传输</text>\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t<view class=\"privacy-item\">\r\n\t\t\t\t\t\t\t\t<text class=\"privacy-bullet\">•</text>\r\n\t\t\t\t\t\t\t\t<text class=\"privacy-desc\">存储权限：用于保存用户偏好设置</text>\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t<text class=\"privacy-promise\">我们承诺严格保护您的隐私信息，不会用于其他用途。</text>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class=\"privacy-consent-footer\">\r\n\t\t\t\t\t<button class=\"btn btn-secondary privacy-btn\" @click=\"rejectPrivacyConsent\">拒绝</button>\r\n\t\t\t\t\t<button class=\"btn btn-primary privacy-btn\" @click=\"acceptPrivacyConsent\">同意</button>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t</view>\r\n\t</view>\r\n</template>\r\n\r\n<script>\r\n\timport {\r\n\t\tregister,\r\n\t\tgetCodeImg,\r\n\t\tsendSmsCode\r\n\t} from '@/api/user'\r\n\timport {\r\n\t\tutils\r\n\t} from '@/api/index'\r\n\r\n\texport default {\r\n\t\tdata() {\r\n\t\t\treturn {\r\n\t\t\t\t// 注册表单\r\n\t\t\t\tregisterForm: {\r\n\t\t\t\t\tusername: '',\r\n\t\t\t\t\tphone: '',\r\n\t\t\t\t\tphoneCode: '',\r\n\t\t\t\t\tpassword: '',\r\n\t\t\t\t\tconfirmPassword: '',\r\n\t\t\t\t\tcode: '',\r\n\t\t\t\t\tuuid: '',\r\n\t\t\t\t\tagreement: false\r\n\t\t\t\t},\r\n\r\n\t\t\t\t// 状态控制\r\n\t\t\t\tregisterLoading: false,\r\n\t\t\t\tshowPassword: false,\r\n\t\t\t\tshowConfirmPassword: false,\r\n\t\t\t\tcaptchaEnabled: true,\r\n\t\t\t\tcodeUrl: '',\r\n\r\n\t\t\t\t// 短信验证码\r\n\t\t\t\tsmsDisabled: false,\r\n\t\t\t\tsmsButtonText: '获取验证码',\r\n\t\t\t\tsmsCountdown: 0,\r\n\r\n\t\t\t\t// 弹窗状态\r\n\t\t\t\tshowAgreementModal: false,\r\n\t\t\t\tshowPrivacyModal: false,\r\n\t\t\t\tshowWechatPrivacyModal: false\r\n\t\t\t}\r\n\t\t},\r\n\r\n\t\tcomputed: {\r\n\t\t\t// 密码强度\r\n\t\t\tpasswordStrength() {\r\n\t\t\t\tconst password = this.registerForm.password\r\n\t\t\t\tif (!password) return 0\r\n\r\n\t\t\t\tlet strength = 0\r\n\t\t\t\t// 长度检查\r\n\t\t\t\tif (password.length >= 8) strength++\r\n\t\t\t\t// 包含数字和字母\r\n\t\t\t\tif (/[0-9]/.test(password) && /[a-zA-Z]/.test(password)) strength++\r\n\t\t\t\t// 包含特殊字符\r\n\t\t\t\tif (/[^a-zA-Z0-9]/.test(password)) strength++\r\n\r\n\t\t\t\treturn strength\r\n\t\t\t},\r\n\r\n\t\t\t// 密码强度文本\r\n\t\t\tpasswordStrengthText() {\r\n\t\t\t\tswitch (this.passwordStrength) {\r\n\t\t\t\t\tcase 0:\r\n\t\t\t\t\tcase 1:\r\n\t\t\t\t\t\treturn '弱'\r\n\t\t\t\t\tcase 2:\r\n\t\t\t\t\t\treturn '中'\r\n\t\t\t\t\tcase 3:\r\n\t\t\t\t\t\treturn '强'\r\n\t\t\t\t\tdefault:\r\n\t\t\t\t\t\treturn ''\r\n\t\t\t\t}\r\n\t\t\t},\r\n\r\n\t\t\t// 是否可以注册\r\n\t\t\tcanRegister() {\r\n\t\t\t\treturn this.registerForm.username &&\r\n\t\t\t\t\tthis.registerForm.phone &&\r\n\t\t\t\t\tthis.registerForm.phoneCode &&\r\n\t\t\t\t\tthis.registerForm.password &&\r\n\t\t\t\t\tthis.registerForm.confirmPassword &&\r\n\t\t\t\t\tthis.registerForm.agreement &&\r\n\t\t\t\t\t(!this.captchaEnabled || this.registerForm.code)\r\n\t\t\t}\r\n\t\t},\r\n\r\n\t\tonLoad() {\r\n\t\t\t// 在微信小程序环境下显示隐私同意弹窗\r\n\t\t\t// #ifdef MP-WEIXIN\r\n\t\t\tthis.showWechatPrivacyModal = true\r\n\t\t\t// #endif\r\n\t\t\t// #ifndef MP-WEIXIN\r\n\t\t\tthis.initPage()\r\n\t\t\t// #endif\r\n\t\t},\r\n\r\n\t\tmethods: {\r\n\t\t\t// 显示错误提示的可靠方法\r\n\t\t\tshowErrorMessage(message, useModal = false) {\r\n\t\t\t\tconsole.log('显示错误信息:', message)\r\n\r\n\t\t\t\tif (useModal) {\r\n\t\t\t\t\tuni.showModal({\r\n\t\t\t\t\t\ttitle: '提示',\r\n\t\t\t\t\t\tcontent: message,\r\n\t\t\t\t\t\tshowCancel: false,\r\n\t\t\t\t\t\tconfirmText: '我知道了'\r\n\t\t\t\t\t})\r\n\t\t\t\t} else {\r\n\t\t\t\t\t// 确保先隐藏loading，再显示toast\r\n\t\t\t\t\tuni.hideLoading()\r\n\t\t\t\t\tsetTimeout(() => {\r\n\t\t\t\t\t\tuni.showToast({\r\n\t\t\t\t\t\t\ttitle: message,\r\n\t\t\t\t\t\t\ticon: 'none',\r\n\t\t\t\t\t\t\tduration: 3000,\r\n\t\t\t\t\t\t\tmask: true\r\n\t\t\t\t\t\t})\r\n\t\t\t\t\t}, 200)\r\n\t\t\t\t}\r\n\t\t\t},\r\n\r\n\t\t\t// 初始化页面\r\n\t\t\tasync initPage() {\r\n\t\t\t\t// 获取验证码\r\n\t\t\t\tawait this.refreshCaptcha()\r\n\t\t\t},\r\n\r\n\t\t\t// 切换密码显示\r\n\t\t\ttogglePassword() {\r\n\t\t\t\tthis.showPassword = !this.showPassword\r\n\t\t\t},\r\n\r\n\t\t\t// 切换确认密码显示\r\n\t\t\ttoggleConfirmPassword() {\r\n\t\t\t\tthis.showConfirmPassword = !this.showConfirmPassword\r\n\t\t\t},\r\n\r\n\t\t\t// 切换协议同意\r\n\t\t\ttoggleAgreement() {\r\n\t\t\t\tthis.registerForm.agreement = !this.registerForm.agreement\r\n\t\t\t},\r\n\r\n\t\t\t// 刷新验证码\r\n\t\t\tasync refreshCaptcha() {\r\n\t\t\t\ttry {\r\n\t\t\t\t\tconst res = await getCodeImg()\r\n\t\t\t\t\tif (res.code === 200) {\r\n\t\t\t\t\t\tthis.captchaEnabled = res.captchaEnabled !== false\r\n\t\t\t\t\t\tif (this.captchaEnabled) {\r\n\t\t\t\t\t\t\tthis.codeUrl = \"data:image/gif;base64,\" + res.img\r\n\t\t\t\t\t\t\tthis.registerForm.uuid = res.uuid\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t}\r\n\t\t\t\t} catch (error) {\r\n\t\t\t\t\tconsole.error('获取验证码失败:', error)\r\n\t\t\t\t}\r\n\t\t\t},\r\n\r\n\t\t\t// 发送短信验证码\r\n\t\t\tasync sendSmsCode() {\r\n\t\t\t\tif (!this.registerForm.phone) {\r\n\t\t\t\t\tuni.showToast({\r\n\t\t\t\t\t\ttitle: '请输入手机号',\r\n\t\t\t\t\t\ticon: 'none'\r\n\t\t\t\t\t})\r\n\t\t\t\t\treturn\r\n\t\t\t\t}\r\n\r\n\t\t\t\tif (!/^1[3-9]\\d{9}$/.test(this.registerForm.phone)) {\r\n\t\t\t\t\tuni.showToast({\r\n\t\t\t\t\t\ttitle: '请输入正确的手机号',\r\n\t\t\t\t\t\ticon: 'none'\r\n\t\t\t\t\t})\r\n\t\t\t\t\treturn\r\n\t\t\t\t}\r\n\r\n\t\t\t\ttry {\r\n\t\t\t\t\tconst res = await sendSmsCode(this.registerForm.phone)\r\n\t\t\t\t\tif (res.code === 200) {\r\n\t\t\t\t\t\tuni.showToast({\r\n\t\t\t\t\t\t\ttitle: '发送成功',\r\n\t\t\t\t\t\t\ticon: 'success'\r\n\t\t\t\t\t\t})\r\n\t\t\t\t\t\tthis.startSmsCountdown()\r\n\t\t\t\t\t} else {\r\n\t\t\t\t\t\tuni.showToast({\r\n\t\t\t\t\t\t\ttitle: res.msg || '发送失败',\r\n\t\t\t\t\t\t\ticon: 'none'\r\n\t\t\t\t\t\t})\r\n\t\t\t\t\t}\r\n\t\t\t\t} catch (error) {\r\n\t\t\t\t\tconsole.error('发送短信失败:', error)\r\n\t\t\t\t\tuni.showToast({\r\n\t\t\t\t\t\ttitle: '发送失败，请稍后再试',\r\n\t\t\t\t\t\ticon: 'none'\r\n\t\t\t\t\t})\r\n\t\t\t\t}\r\n\t\t\t},\r\n\r\n\t\t\t// 开始短信倒计时\r\n\t\t\tstartSmsCountdown() {\r\n\t\t\t\tthis.smsCountdown = 60\r\n\t\t\t\tthis.smsDisabled = true\r\n\t\t\t\tthis.smsButtonText = `${this.smsCountdown}s`\r\n\r\n\t\t\t\tconst timer = setInterval(() => {\r\n\t\t\t\t\tthis.smsCountdown--\r\n\t\t\t\t\tthis.smsButtonText = `${this.smsCountdown}s`\r\n\r\n\t\t\t\t\tif (this.smsCountdown <= 0) {\r\n\t\t\t\t\t\tclearInterval(timer)\r\n\t\t\t\t\t\tthis.smsDisabled = false\r\n\t\t\t\t\t\tthis.smsButtonText = '获取验证码'\r\n\t\t\t\t\t}\r\n\t\t\t\t}, 1000)\r\n\t\t\t},\r\n\r\n\t\t\t// 处理注册\r\n\t\t\tasync handleRegister() {\r\n\t\t\t\t// 表单验证\r\n\t\t\t\tif (!this.validateForm()) {\r\n\t\t\t\t\treturn\r\n\t\t\t\t}\r\n\r\n\t\t\t\tthis.registerLoading = true\r\n\r\n\t\t\t\ttry {\r\n\t\t\t\t\tconst res = await register({\r\n\t\t\t\t\t\tusername: this.registerForm.username,\r\n\t\t\t\t\t\tphone: this.registerForm.phone,\r\n\t\t\t\t\t\tphoneCode: this.registerForm.phoneCode,\r\n\t\t\t\t\t\tpassword: this.registerForm.password,\r\n\t\t\t\t\t\tcode: this.registerForm.code,\r\n\t\t\t\t\t\tuuid: this.registerForm.uuid\r\n\t\t\t\t\t})\r\n\r\n\t\t\t\t\tif (res && res.code === 200) {\r\n\t\t\t\t\t\tuni.showModal({\r\n\t\t\t\t\t\t\ttitle: '注册成功',\r\n\t\t\t\t\t\t\tcontent: '恭喜您注册成功！请前往登录页面登录。',\r\n\t\t\t\t\t\t\tshowCancel: false,\r\n\t\t\t\t\t\t\tsuccess: () => {\r\n\t\t\t\t\t\t\t\tthis.goToLogin()\r\n\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t})\r\n\t\t\t\t\t} else {\r\n\t\t\t\t\t\t// 显示服务器返回的具体错误信息\r\n\t\t\t\t\t\tconst errorMsg = (res && res.msg) || '注册失败，请检查输入信息'\r\n\t\t\t\t\t\tconsole.log('注册失败，服务器返回:', errorMsg)\r\n\r\n\t\t\t\t\t\t// 使用可靠的错误提示方法\r\n\t\t\t\t\t\tthis.showErrorMessage(errorMsg, true) // 使用modal显示\r\n\r\n\t\t\t\t\t\t// 刷新验证码\r\n\t\t\t\t\t\tif (this.captchaEnabled) {\r\n\t\t\t\t\t\t\tawait this.refreshCaptcha()\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t}\r\n\t\t\t\t} catch (error) {\r\n\t\t\t\t\tconsole.error('注册失败:', error)\r\n\t\t\t\t\t// 显示具体的错误信息，优先显示服务器返回的错误信息\r\n\t\t\t\t\tlet errorMessage = '注册失败，请稍后再试'\r\n\r\n\t\t\t\t\t// 如果error.message包含服务器返回的错误信息，则显示它\r\n\t\t\t\t\tif (error.message) {\r\n\t\t\t\t\t\terrorMessage = error.message\r\n\t\t\t\t\t}\r\n\r\n\t\t\t\t\tconsole.log('准备显示错误提示:', errorMessage)\r\n\r\n\t\t\t\t\t// 使用可靠的错误提示方法\r\n\t\t\t\t\tthis.showErrorMessage(errorMessage, true) // 使用modal显示\r\n\r\n\t\t\t\t\t// 刷新验证码\r\n\t\t\t\t\tif (this.captchaEnabled) {\r\n\t\t\t\t\t\tawait this.refreshCaptcha()\r\n\t\t\t\t\t}\r\n\t\t\t\t} finally {\r\n\t\t\t\t\tthis.registerLoading = false\r\n\t\t\t\t}\r\n\t\t\t},\r\n\r\n\t\t\t// 表单验证\r\n\t\t\tvalidateForm() {\r\n\t\t\t\tif (!this.registerForm.username) {\r\n\t\t\t\t\tuni.showToast({\r\n\t\t\t\t\t\ttitle: '请输入用户名',\r\n\t\t\t\t\t\ticon: 'none'\r\n\t\t\t\t\t})\r\n\t\t\t\t\treturn false\r\n\t\t\t\t}\r\n\r\n\t\t\t\tif (this.registerForm.username.length < 3) {\r\n\t\t\t\t\tuni.showToast({\r\n\t\t\t\t\t\ttitle: '用户名至少3个字符',\r\n\t\t\t\t\t\ticon: 'none'\r\n\t\t\t\t\t})\r\n\t\t\t\t\treturn false\r\n\t\t\t\t}\r\n\r\n\t\t\t\tif (!this.registerForm.phone) {\r\n\t\t\t\t\tuni.showToast({\r\n\t\t\t\t\t\ttitle: '请输入手机号',\r\n\t\t\t\t\t\ticon: 'none'\r\n\t\t\t\t\t})\r\n\t\t\t\t\treturn false\r\n\t\t\t\t}\r\n\r\n\t\t\t\tif (!/^1[3-9]\\d{9}$/.test(this.registerForm.phone)) {\r\n\t\t\t\t\tuni.showToast({\r\n\t\t\t\t\t\ttitle: '请输入正确的手机号',\r\n\t\t\t\t\t\ticon: 'none'\r\n\t\t\t\t\t})\r\n\t\t\t\t\treturn false\r\n\t\t\t\t}\r\n\r\n\t\t\t\tif (!this.registerForm.phoneCode) {\r\n\t\t\t\t\tuni.showToast({\r\n\t\t\t\t\t\ttitle: '请输入短信验证码',\r\n\t\t\t\t\t\ticon: 'none'\r\n\t\t\t\t\t})\r\n\t\t\t\t\treturn false\r\n\t\t\t\t}\r\n\r\n\t\t\t\tif (!this.registerForm.password) {\r\n\t\t\t\t\tuni.showToast({\r\n\t\t\t\t\t\ttitle: '请输入密码',\r\n\t\t\t\t\t\ticon: 'none'\r\n\t\t\t\t\t})\r\n\t\t\t\t\treturn false\r\n\t\t\t\t}\r\n\r\n\t\t\t\tif (this.registerForm.password.length < 6) {\r\n\t\t\t\t\tuni.showToast({\r\n\t\t\t\t\t\ttitle: '密码至少6个字符',\r\n\t\t\t\t\t\ticon: 'none'\r\n\t\t\t\t\t})\r\n\t\t\t\t\treturn false\r\n\t\t\t\t}\r\n\r\n\t\t\t\tif (!this.registerForm.confirmPassword) {\r\n\t\t\t\t\tuni.showToast({\r\n\t\t\t\t\t\ttitle: '请确认密码',\r\n\t\t\t\t\t\ticon: 'none'\r\n\t\t\t\t\t})\r\n\t\t\t\t\treturn false\r\n\t\t\t\t}\r\n\r\n\t\t\t\tif (this.registerForm.password !== this.registerForm.confirmPassword) {\r\n\t\t\t\t\tuni.showToast({\r\n\t\t\t\t\t\ttitle: '两次密码输入不一致',\r\n\t\t\t\t\t\ticon: 'none'\r\n\t\t\t\t\t})\r\n\t\t\t\t\treturn false\r\n\t\t\t\t}\r\n\r\n\t\t\t\tif (this.captchaEnabled && !this.registerForm.code) {\r\n\t\t\t\t\tuni.showToast({\r\n\t\t\t\t\t\ttitle: '请输入验证码',\r\n\t\t\t\t\t\ticon: 'none'\r\n\t\t\t\t\t})\r\n\t\t\t\t\treturn false\r\n\t\t\t\t}\r\n\r\n\t\t\t\tif (!this.registerForm.agreement) {\r\n\t\t\t\t\tuni.showToast({\r\n\t\t\t\t\t\ttitle: '请同意用户协议和隐私政策',\r\n\t\t\t\t\t\ticon: 'none'\r\n\t\t\t\t\t})\r\n\t\t\t\t\treturn false\r\n\t\t\t\t}\r\n\r\n\t\t\t\treturn true\r\n\t\t\t},\r\n\r\n\t\t\t// 显示用户协议\r\n\t\t\tshowAgreement() {\r\n\t\t\t\tthis.showAgreementModal = true\r\n\t\t\t},\r\n\r\n\t\t\t// 关闭用户协议\r\n\t\t\tcloseAgreementModal() {\r\n\t\t\t\tthis.showAgreementModal = false\r\n\t\t\t},\r\n\r\n\t\t\t// 显示隐私政策\r\n\t\t\tshowPrivacy() {\r\n\t\t\t\tthis.showPrivacyModal = true\r\n\t\t\t},\r\n\r\n\t\t\t// 关闭隐私政策\r\n\t\t\tclosePrivacyModal() {\r\n\t\t\t\tthis.showPrivacyModal = false\r\n\t\t\t},\r\n\r\n\t\t\t// 前往登录页面\r\n\t\t\tgoToLogin() {\r\n\t\t\t\tuni.navigateBack()\r\n\t\t\t},\r\n\r\n\t\t\t// 接受隐私同意\r\n\t\t\tacceptPrivacyConsent() {\r\n\t\t\t\tthis.showWechatPrivacyModal = false\r\n\t\t\t\t// 初始化页面\r\n\t\t\t\tthis.initPage()\r\n\t\t\t},\r\n\r\n\t\t\t// 拒绝隐私同意\r\n\t\t\trejectPrivacyConsent() {\r\n\t\t\t\tuni.showModal({\r\n\t\t\t\t\ttitle: '提示',\r\n\t\t\t\t\tcontent: '拒绝授权将无法使用注册功能，是否返回登录页面？',\r\n\t\t\t\t\tconfirmText: '返回登录',\r\n\t\t\t\t\tcancelText: '重新考虑',\r\n\t\t\t\t\tsuccess: (res) => {\r\n\t\t\t\t\t\tif (res.confirm) {\r\n\t\t\t\t\t\t\t// 返回登录页面\r\n\t\t\t\t\t\t\tthis.goToLogin()\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t\t// 如果用户选择\"重新考虑\"，弹窗会自动关闭，隐私弹窗保持显示\r\n\t\t\t\t\t}\r\n\t\t\t\t})\r\n\t\t\t}\r\n\t\t}\r\n\t}\r\n</script>\r\n\r\n<style scoped>\r\n\t/* 注册容器 */\r\n\t.register-container {\r\n\t\tmin-height: 100vh;\r\n\t\tbackground: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\r\n\t\tdisplay: flex;\r\n\t\talign-items: center;\r\n\t\tjustify-content: center;\r\n\t\tpadding: 40rpx;\r\n\t\tposition: relative;\r\n\t\toverflow: hidden;\r\n\t}\r\n\r\n\t/* 背景装饰 */\r\n\t.bg-decoration {\r\n\t\tposition: absolute;\r\n\t\ttop: 0;\r\n\t\tleft: 0;\r\n\t\tright: 0;\r\n\t\tbottom: 0;\r\n\t\tpointer-events: none;\r\n\t}\r\n\r\n\t.circle {\r\n\t\tposition: absolute;\r\n\t\tborder-radius: 50%;\r\n\t\tbackground: rgba(255, 255, 255, 0.1);\r\n\t\tanimation: float 6s ease-in-out infinite;\r\n\t}\r\n\r\n\t.circle-1 {\r\n\t\twidth: 200rpx;\r\n\t\theight: 200rpx;\r\n\t\ttop: 10%;\r\n\t\tleft: 10%;\r\n\t\tanimation-delay: 0s;\r\n\t}\r\n\r\n\t.circle-2 {\r\n\t\twidth: 150rpx;\r\n\t\theight: 150rpx;\r\n\t\ttop: 60%;\r\n\t\tright: 15%;\r\n\t\tanimation-delay: 2s;\r\n\t}\r\n\r\n\t.circle-3 {\r\n\t\twidth: 100rpx;\r\n\t\theight: 100rpx;\r\n\t\tbottom: 20%;\r\n\t\tleft: 20%;\r\n\t\tanimation-delay: 4s;\r\n\t}\r\n\r\n\t@keyframes float {\r\n\r\n\t\t0%,\r\n\t\t100% {\r\n\t\t\ttransform: translateY(0px) rotate(0deg);\r\n\t\t}\r\n\r\n\t\t50% {\r\n\t\t\ttransform: translateY(-20px) rotate(180deg);\r\n\t\t}\r\n\t}\r\n\r\n\t/* 注册卡片 */\r\n\t.register-card {\r\n\t\tbackground: rgba(255, 255, 255, 0.95);\r\n\t\tbackdrop-filter: blur(10px);\r\n\t\tborder-radius: 24rpx;\r\n\t\tpadding: 60rpx 40rpx;\r\n\t\twidth: 100%;\r\n\t\tmax-width: 600rpx;\r\n\t\tmax-height: 90vh;\r\n\t\toverflow-y: auto;\r\n\t\tbox-shadow: 0 20rpx 60rpx rgba(0, 0, 0, 0.1);\r\n\t\tposition: relative;\r\n\t\tz-index: 1;\r\n\t}\r\n\r\n\t.fade-in {\r\n\t\tanimation: fadeInUp 0.8s ease-out;\r\n\t}\r\n\r\n\t@keyframes fadeInUp {\r\n\t\tfrom {\r\n\t\t\topacity: 0;\r\n\t\t\ttransform: translateY(50rpx);\r\n\t\t}\r\n\r\n\t\tto {\r\n\t\t\topacity: 1;\r\n\t\t\ttransform: translateY(0);\r\n\t\t}\r\n\t}\r\n\r\n\t/* 注册头部 */\r\n\t.register-header {\r\n\t\ttext-align: center;\r\n\t\tmargin-bottom: 50rpx;\r\n\t}\r\n\r\n\t.logo {\r\n\t\tfont-size: 80rpx;\r\n\t\tmargin-bottom: 20rpx;\r\n\t}\r\n\r\n\t.title {\r\n\t\tfont-size: 48rpx;\r\n\t\tfont-weight: bold;\r\n\t\tcolor: #333333;\r\n\t\tmargin-bottom: 10rpx;\r\n\t}\r\n\r\n\t.subtitle {\r\n\t\tfont-size: 28rpx;\r\n\t\tcolor: #666666;\r\n\t}\r\n\r\n\t/* 表单容器 */\r\n\t.form-container {\r\n\t\tmargin-bottom: 40rpx;\r\n\t}\r\n\r\n\t/* 输入组 */\r\n\t.input-group {\r\n\t\tdisplay: flex;\r\n\t\talign-items: center;\r\n\t\tbackground: #f8f9fa;\r\n\t\tborder-radius: 16rpx;\r\n\t\tpadding: 0 20rpx;\r\n\t\tmargin-bottom: 30rpx;\r\n\t\tborder: 2rpx solid transparent;\r\n\t\ttransition: all 0.3s ease;\r\n\t}\r\n\r\n\t.input-group:focus-within {\r\n\t\tborder-color: #667eea;\r\n\t\tbackground: #ffffff;\r\n\t\tbox-shadow: 0 0 0 4rpx rgba(102, 126, 234, 0.1);\r\n\t}\r\n\r\n\t.input-icon {\r\n\t\tfont-size: 32rpx;\r\n\t\tmargin-right: 20rpx;\r\n\t\tcolor: #999999;\r\n\t}\r\n\r\n\t.input-field {\r\n\t\tflex: 1;\r\n\t\theight: 80rpx;\r\n\t\tfont-size: 28rpx;\r\n\t\tcolor: #333333;\r\n\t\tbackground: transparent;\r\n\t\tborder: none;\r\n\t}\r\n\r\n\t.input-suffix {\r\n\t\tfont-size: 32rpx;\r\n\t\tcolor: #999999;\r\n\t\tcursor: pointer;\r\n\t\tpadding: 10rpx;\r\n\t}\r\n\r\n\t/* 验证码相关 */\r\n\t.captcha-group {\r\n\t\tpadding-right: 0;\r\n\t}\r\n\r\n\t.captcha-input {\r\n\t\tflex: 1;\r\n\t\tmargin-right: 20rpx;\r\n\t}\r\n\r\n\t.captcha-image {\r\n\t\twidth: 160rpx;\r\n\t\theight: 80rpx;\r\n\t\tborder-radius: 8rpx;\r\n\t\toverflow: hidden;\r\n\t\tcursor: pointer;\r\n\t\tdisplay: flex;\r\n\t\talign-items: center;\r\n\t\tjustify-content: center;\r\n\t\tbackground: #e9ecef;\r\n\t}\r\n\r\n\t.captcha-img {\r\n\t\twidth: 100%;\r\n\t\theight: 100%;\r\n\t}\r\n\r\n\t.captcha-placeholder {\r\n\t\tfont-size: 24rpx;\r\n\t\tcolor: #999999;\r\n\t}\r\n\r\n\t/* 短信验证码 */\r\n\t.sms-group {\r\n\t\tpadding-right: 0;\r\n\t}\r\n\r\n\t.sms-input {\r\n\t\tflex: 1;\r\n\t\tmargin-right: 20rpx;\r\n\t}\r\n\r\n\t.sms-btn {\r\n\t\tpadding: 20rpx 30rpx;\r\n\t\tbackground: #667eea;\r\n\t\tcolor: #ffffff;\r\n\t\tborder: none;\r\n\t\tborder-radius: 12rpx;\r\n\t\tfont-size: 24rpx;\r\n\t\tcursor: pointer;\r\n\t\ttransition: all 0.3s ease;\r\n\t}\r\n\r\n\t.sms-btn:disabled {\r\n\t\tbackground: #c0c4cc;\r\n\t\tcursor: not-allowed;\r\n\t}\r\n\r\n\t/* 密码强度 */\r\n\t.password-strength {\r\n\t\tdisplay: flex;\r\n\t\talign-items: center;\r\n\t\tmargin-bottom: 30rpx;\r\n\t\tpadding: 0 20rpx;\r\n\t}\r\n\r\n\t.strength-label {\r\n\t\tfont-size: 24rpx;\r\n\t\tcolor: #666666;\r\n\t\tmargin-right: 15rpx;\r\n\t}\r\n\r\n\t.strength-bar {\r\n\t\tdisplay: flex;\r\n\t\tgap: 8rpx;\r\n\t\tmargin-right: 15rpx;\r\n\t}\r\n\r\n\t.strength-item {\r\n\t\twidth: 40rpx;\r\n\t\theight: 8rpx;\r\n\t\tbackground: #e9ecef;\r\n\t\tborder-radius: 4rpx;\r\n\t\ttransition: all 0.3s ease;\r\n\t}\r\n\r\n\t.strength-item.active:nth-child(1) {\r\n\t\tbackground: #f56c6c;\r\n\t}\r\n\r\n\t.strength-item.active:nth-child(2) {\r\n\t\tbackground: #e6a23c;\r\n\t}\r\n\r\n\t.strength-item.active:nth-child(3) {\r\n\t\tbackground: #67c23a;\r\n\t}\r\n\r\n\t.strength-text {\r\n\t\tfont-size: 24rpx;\r\n\t\tcolor: #666666;\r\n\t}\r\n\r\n\t/* 协议组 */\r\n\t.agreement-group {\r\n\t\tdisplay: flex;\r\n\t\talign-items: flex-start;\r\n\t\tmargin-bottom: 40rpx;\r\n\t\tpadding: 0 20rpx;\r\n\t}\r\n\r\n\t.checkbox {\r\n\t\twidth: 36rpx;\r\n\t\theight: 36rpx;\r\n\t\tborder: 2rpx solid #ddd;\r\n\t\tborder-radius: 6rpx;\r\n\t\tdisplay: flex;\r\n\t\talign-items: center;\r\n\t\tjustify-content: center;\r\n\t\tmargin-right: 15rpx;\r\n\t\tmargin-top: 4rpx;\r\n\t\tcursor: pointer;\r\n\t\ttransition: all 0.3s ease;\r\n\t\tflex-shrink: 0;\r\n\t}\r\n\r\n\t.checkbox.checked {\r\n\t\tbackground: #667eea;\r\n\t\tborder-color: #667eea;\r\n\t}\r\n\r\n\t.checkbox-icon {\r\n\t\tcolor: #ffffff;\r\n\t\tfont-size: 20rpx;\r\n\t\tfont-weight: bold;\r\n\t}\r\n\r\n\t.agreement-text {\r\n\t\tfont-size: 26rpx;\r\n\t\tcolor: #666666;\r\n\t\tline-height: 1.6;\r\n\t}\r\n\r\n\t.agreement-link {\r\n\t\tcolor: #667eea;\r\n\t\tcursor: pointer;\r\n\t}\r\n\r\n\t/* 注册按钮 */\r\n\t.register-btn {\r\n\t\twidth: 100%;\r\n\t\theight: 88rpx;\r\n\t\tbackground: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\r\n\t\tcolor: #ffffff;\r\n\t\tborder: none;\r\n\t\tborder-radius: 44rpx;\r\n\t\tfont-size: 32rpx;\r\n\t\tfont-weight: bold;\r\n\t\tdisplay: flex;\r\n\t\talign-items: center;\r\n\t\tjustify-content: center;\r\n\t\tcursor: pointer;\r\n\t\ttransition: all 0.3s ease;\r\n\t\tbox-shadow: 0 8rpx 24rpx rgba(102, 126, 234, 0.3);\r\n\t}\r\n\r\n\t.register-btn:hover {\r\n\t\ttransform: translateY(-2rpx);\r\n\t\tbox-shadow: 0 12rpx 32rpx rgba(102, 126, 234, 0.4);\r\n\t}\r\n\r\n\t.register-btn.loading,\r\n\t.register-btn:disabled {\r\n\t\tbackground: #c0c4cc;\r\n\t\tcursor: not-allowed;\r\n\t\ttransform: none;\r\n\t\tbox-shadow: 0 8rpx 24rpx rgba(192, 196, 204, 0.3);\r\n\t}\r\n\r\n\t.loading-icon {\r\n\t\tmargin-right: 15rpx;\r\n\t\tanimation: spin 1s linear infinite;\r\n\t}\r\n\r\n\t@keyframes spin {\r\n\t\tfrom {\r\n\t\t\ttransform: rotate(0deg);\r\n\t\t}\r\n\r\n\t\tto {\r\n\t\t\ttransform: rotate(360deg);\r\n\t\t}\r\n\t}\r\n\r\n\t/* 底部 */\r\n\t.register-footer {\r\n\t\ttext-align: center;\r\n\t\tmargin-top: 40rpx;\r\n\t}\r\n\r\n\t.footer-link {\r\n\t\tcolor: #667eea;\r\n\t\tfont-size: 26rpx;\r\n\t\tcursor: pointer;\r\n\t\ttransition: all 0.3s ease;\r\n\t}\r\n\r\n\t.footer-link:hover {\r\n\t\tcolor: #764ba2;\r\n\t}\r\n\r\n\t/* 弹窗样式 */\r\n\t.modal-overlay {\r\n\t\tposition: fixed;\r\n\t\ttop: 0;\r\n\t\tleft: 0;\r\n\t\tright: 0;\r\n\t\tbottom: 0;\r\n\t\tbackground-color: rgba(0, 0, 0, 0.5);\r\n\t\tdisplay: flex;\r\n\t\tjustify-content: center;\r\n\t\talign-items: center;\r\n\t\tz-index: 1000;\r\n\t}\r\n\r\n\t.modal-content {\r\n\t\tbackground-color: #ffffff;\r\n\t\tborder-radius: 16rpx;\r\n\t\twidth: 90%;\r\n\t\tmax-width: 600rpx;\r\n\t\tmax-height: 80vh;\r\n\t\toverflow-y: auto;\r\n\t\tbox-shadow: 0 10rpx 40rpx rgba(0, 0, 0, 0.3);\r\n\t\tanimation: modalSlideIn 0.3s ease-out;\r\n\t}\r\n\r\n\t@keyframes modalSlideIn {\r\n\t\tfrom {\r\n\t\t\topacity: 0;\r\n\t\t\ttransform: translateY(-50rpx) scale(0.9);\r\n\t\t}\r\n\r\n\t\tto {\r\n\t\t\topacity: 1;\r\n\t\t\ttransform: translateY(0) scale(1);\r\n\t\t}\r\n\t}\r\n\r\n\t.modal-header {\r\n\t\tdisplay: flex;\r\n\t\tjustify-content: space-between;\r\n\t\talign-items: center;\r\n\t\tpadding: 30rpx;\r\n\t\tborder-bottom: 1rpx solid #f0f0f0;\r\n\t}\r\n\r\n\t.modal-title {\r\n\t\tfont-size: 32rpx;\r\n\t\tfont-weight: bold;\r\n\t\tcolor: #333333;\r\n\t}\r\n\r\n\t.modal-close {\r\n\t\tfont-size: 48rpx;\r\n\t\tcolor: #999999;\r\n\t\tcursor: pointer;\r\n\t\twidth: 60rpx;\r\n\t\theight: 60rpx;\r\n\t\tdisplay: flex;\r\n\t\tjustify-content: center;\r\n\t\talign-items: center;\r\n\t\tborder-radius: 50%;\r\n\t\ttransition: all 0.3s ease;\r\n\t}\r\n\r\n\t.modal-close:hover {\r\n\t\tbackground-color: #f5f5f5;\r\n\t\tcolor: #666666;\r\n\t}\r\n\r\n\t.modal-body {\r\n\t\tpadding: 30rpx;\r\n\t}\r\n\r\n\t.modal-footer {\r\n\t\tpadding: 30rpx;\r\n\t\tborder-top: 1rpx solid #f0f0f0;\r\n\t\tdisplay: flex;\r\n\t\tjustify-content: center;\r\n\t}\r\n\r\n\t.agreement-content,\r\n\t.privacy-content {\r\n\t\tline-height: 1.8;\r\n\t}\r\n\r\n\t.agreement-content text,\r\n\t.privacy-content text {\r\n\t\tdisplay: block;\r\n\t\tmargin-bottom: 20rpx;\r\n\t\tfont-size: 28rpx;\r\n\t\tcolor: #333333;\r\n\t}\r\n\r\n\t.btn {\r\n\t\tpadding: 20rpx 40rpx;\r\n\t\tborder-radius: 25rpx;\r\n\t\tborder: none;\r\n\t\tfont-size: 28rpx;\r\n\t\tfont-weight: bold;\r\n\t\tcursor: pointer;\r\n\t\ttransition: all 0.3s ease;\r\n\t\ttext-align: center;\r\n\t\tdisplay: inline-block;\r\n\t\tbox-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.1);\r\n\t}\r\n\r\n\t.btn-primary {\r\n\t\tbackground: linear-gradient(135deg, #409EFF 0%, #66b1ff 100%);\r\n\t\tcolor: #ffffff;\r\n\t}\r\n\r\n\t.btn-secondary {\r\n\t\tbackground: #f5f5f5;\r\n\t\tcolor: #666666;\r\n\t\tborder: 1rpx solid #ddd;\r\n\t}\r\n\r\n\t.btn-secondary:hover {\r\n\t\tbackground: #e9ecef;\r\n\t\tborder-color: #ccc;\r\n\t}\r\n\r\n\t/* 微信隐私同意弹窗样式 */\r\n\t.privacy-consent-modal {\r\n\t\tz-index: 2000;\r\n\t}\r\n\r\n\t.privacy-consent-content {\r\n\t\tmax-width: 500rpx;\r\n\t\tborder-radius: 20rpx;\r\n\t\toverflow: hidden;\r\n\t}\r\n\r\n\t.privacy-consent-header {\r\n\t\ttext-align: center;\r\n\t\tpadding: 40rpx 30rpx 20rpx;\r\n\t\tbackground: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\r\n\t\tcolor: #ffffff;\r\n\t}\r\n\r\n\t.privacy-icon {\r\n\t\tfont-size: 60rpx;\r\n\t\tmargin-bottom: 15rpx;\r\n\t}\r\n\r\n\t.privacy-title {\r\n\t\tfont-size: 32rpx;\r\n\t\tfont-weight: bold;\r\n\t}\r\n\r\n\t.privacy-consent-body {\r\n\t\tpadding: 30rpx;\r\n\t}\r\n\r\n\t.privacy-notice {\r\n\t\tline-height: 1.6;\r\n\t}\r\n\r\n\t.privacy-text {\r\n\t\tfont-size: 28rpx;\r\n\t\tcolor: #333333;\r\n\t\tmargin-bottom: 20rpx;\r\n\t\tdisplay: block;\r\n\t}\r\n\r\n\t.privacy-list {\r\n\t\tmargin: 20rpx 0;\r\n\t\tpadding-left: 10rpx;\r\n\t}\r\n\r\n\t.privacy-item {\r\n\t\tdisplay: flex;\r\n\t\talign-items: flex-start;\r\n\t\tmargin-bottom: 15rpx;\r\n\t}\r\n\r\n\t.privacy-bullet {\r\n\t\tcolor: #667eea;\r\n\t\tfont-size: 28rpx;\r\n\t\tmargin-right: 10rpx;\r\n\t\tmargin-top: 2rpx;\r\n\t}\r\n\r\n\t.privacy-desc {\r\n\t\tfont-size: 26rpx;\r\n\t\tcolor: #666666;\r\n\t\tflex: 1;\r\n\t}\r\n\r\n\t.privacy-promise {\r\n\t\tfont-size: 26rpx;\r\n\t\tcolor: #999999;\r\n\t\tmargin-top: 20rpx;\r\n\t\tdisplay: block;\r\n\t\tfont-style: italic;\r\n\t}\r\n\r\n\t.privacy-consent-footer {\r\n\t\tpadding: 20rpx 30rpx 30rpx;\r\n\t\tdisplay: flex;\r\n\t\tgap: 20rpx;\r\n\t}\r\n\r\n\t.privacy-btn {\r\n\t\tflex: 1;\r\n\t\theight: 80rpx;\r\n\t\tborder-radius: 40rpx;\r\n\t\tfont-size: 28rpx;\r\n\t\tfont-weight: bold;\r\n\t}\r\n\r\n\t/* 响应式设计 */\r\n\t@media screen and (max-width: 750rpx) {\r\n\t\t.register-card {\r\n\t\t\tpadding: 40rpx 30rpx;\r\n\t\t}\r\n\r\n\t\t.title {\r\n\t\t\tfont-size: 42rpx;\r\n\t\t}\r\n\r\n\t\t.subtitle {\r\n\t\t\tfont-size: 26rpx;\r\n\t\t}\r\n\r\n\t\t.agreement-text {\r\n\t\t\tfont-size: 24rpx;\r\n\t\t}\r\n\t}\r\n</style>", "import mod from \"-!../../../../../app/HBuilderX/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--6-oneOf-1-0!../../../../../app/HBuilderX/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--6-oneOf-1-1!../../../../../app/HBuilderX/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../app/HBuilderX/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--6-oneOf-1-2!../../../../../app/HBuilderX/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--6-oneOf-1-3!../../../../../app/HBuilderX/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../app/HBuilderX/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./register.vue?vue&type=style&index=0&id=891c2434&scoped=true&lang=css&\"; export default mod; export * from \"-!../../../../../app/HBuilderX/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--6-oneOf-1-0!../../../../../app/HBuilderX/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--6-oneOf-1-1!../../../../../app/HBuilderX/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../app/HBuilderX/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--6-oneOf-1-2!../../../../../app/HBuilderX/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--6-oneOf-1-3!../../../../../app/HBuilderX/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../app/HBuilderX/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./register.vue?vue&type=style&index=0&id=891c2434&scoped=true&lang=css&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1755741472338\n      var cssReload = require(\"D:/app/HBuilderX/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}