<view class="register-container data-v-891c2434"><view class="bg-decoration data-v-891c2434"><view class="circle circle-1 data-v-891c2434"></view><view class="circle circle-2 data-v-891c2434"></view><view class="circle circle-3 data-v-891c2434"></view></view><view class="register-card fade-in data-v-891c2434"><view class="register-header data-v-891c2434"><view class="logo data-v-891c2434">📝</view><view class="title data-v-891c2434">创建新账户</view><view class="subtitle data-v-891c2434">请填写以下信息完成注册</view></view><view class="form-container data-v-891c2434"><view class="input-group data-v-891c2434"><view class="input-icon data-v-891c2434">👤</view><input class="input-field data-v-891c2434" placeholder="请输入用户名" data-event-opts="{{[['input',[['__set_model',['$0','username','$event',[]],['registerForm']]]]]}}" value="{{registerForm.username}}" bindinput="__e"/></view><view class="input-group data-v-891c2434"><view class="input-icon data-v-891c2434">📱</view><input class="input-field data-v-891c2434" placeholder="请输入手机号" type="number" data-event-opts="{{[['input',[['__set_model',['$0','phone','$event',[]],['registerForm']]]]]}}" value="{{registerForm.phone}}" bindinput="__e"/></view><view class="input-group sms-group data-v-891c2434"><view class="input-icon data-v-891c2434">💬</view><input class="input-field sms-input data-v-891c2434" placeholder="请输入短信验证码" data-event-opts="{{[['input',[['__set_model',['$0','phoneCode','$event',[]],['registerForm']]]]]}}" value="{{registerForm.phoneCode}}" bindinput="__e"/><button class="sms-btn data-v-891c2434" disabled="{{smsDisabled}}" data-event-opts="{{[['tap',[['sendSmsCode',['$event']]]]]}}" bindtap="__e">{{''+smsButtonText+''}}</button></view><view class="input-group data-v-891c2434"><view class="input-icon data-v-891c2434">🔒</view><input class="input-field data-v-891c2434" placeholder="请输入密码" password="{{!showPassword}}" data-event-opts="{{[['input',[['__set_model',['$0','password','$event',[]],['registerForm']]]]]}}" value="{{registerForm.password}}" bindinput="__e"/><view data-event-opts="{{[['tap',[['togglePassword',['$event']]]]]}}" class="input-suffix data-v-891c2434" bindtap="__e">{{''+(showPassword?'🙈':'👁️')+''}}</view></view><view class="input-group data-v-891c2434"><view class="input-icon data-v-891c2434">🔐</view><input class="input-field data-v-891c2434" placeholder="请确认密码" password="{{!showConfirmPassword}}" data-event-opts="{{[['input',[['__set_model',['$0','confirmPassword','$event',[]],['registerForm']]]]]}}" value="{{registerForm.confirmPassword}}" bindinput="__e"/><view data-event-opts="{{[['tap',[['toggleConfirmPassword',['$event']]]]]}}" class="input-suffix data-v-891c2434" bindtap="__e">{{''+(showConfirmPassword?'🙈':'👁️')+''}}</view></view><block wx:if="{{captchaEnabled}}"><view class="input-group captcha-group data-v-891c2434"><view class="input-icon data-v-891c2434">🔢</view><input class="input-field captcha-input data-v-891c2434" placeholder="请输入验证码" data-event-opts="{{[['input',[['__set_model',['$0','code','$event',[]],['registerForm']]]]]}}" value="{{registerForm.code}}" bindinput="__e"/><view data-event-opts="{{[['tap',[['refreshCaptcha',['$event']]]]]}}" class="captcha-image data-v-891c2434" bindtap="__e"><block wx:if="{{codeUrl}}"><image class="captcha-img data-v-891c2434" src="{{codeUrl}}" mode="aspectFit"></image></block><block wx:else><view class="captcha-placeholder data-v-891c2434">点击获取</view></block></view></view></block><block wx:if="{{registerForm.password}}"><view class="password-strength data-v-891c2434"><view class="strength-label data-v-891c2434">密码强度：</view><view class="strength-bar data-v-891c2434"><view class="{{['strength-item','data-v-891c2434',(passwordStrength>=1)?'active':'']}}"></view><view class="{{['strength-item','data-v-891c2434',(passwordStrength>=2)?'active':'']}}"></view><view class="{{['strength-item','data-v-891c2434',(passwordStrength>=3)?'active':'']}}"></view></view><view class="strength-text data-v-891c2434">{{passwordStrengthText}}</view></view></block><view class="agreement-group data-v-891c2434"><view data-event-opts="{{[['tap',[['toggleAgreement',['$event']]]]]}}" class="{{['checkbox','data-v-891c2434',(registerForm.agreement)?'checked':'']}}" bindtap="__e"><view class="checkbox-icon data-v-891c2434">{{registerForm.agreement?'✓':''}}</view></view><text class="agreement-text data-v-891c2434">我已阅读并同意<text data-event-opts="{{[['tap',[['showAgreement',['$event']]]]]}}" class="agreement-link data-v-891c2434" bindtap="__e">《用户协议》</text>和<text data-event-opts="{{[['tap',[['showPrivacy',['$event']]]]]}}" class="agreement-link data-v-891c2434" bindtap="__e">《隐私政策》</text></text></view><button class="{{['register-btn','data-v-891c2434',(registerLoading)?'loading':'']}}" disabled="{{registerLoading||!canRegister}}" data-event-opts="{{[['tap',[['handleRegister',['$event']]]]]}}" bindtap="__e"><block wx:if="{{registerLoading}}"><view class="loading-icon data-v-891c2434">⏳</view></block>{{''+(registerLoading?'注册中...':'立即注册')+''}}</button></view><view class="register-footer data-v-891c2434"><view data-event-opts="{{[['tap',[['goToLogin',['$event']]]]]}}" class="footer-link data-v-891c2434" bindtap="__e">已有账号？立即登录</view></view></view><block wx:if="{{showAgreementModal}}"><view data-event-opts="{{[['tap',[['closeAgreementModal',['$event']]]]]}}" class="modal-overlay data-v-891c2434" bindtap="__e"><view data-event-opts="{{[['tap',[['',['$event']]]]]}}" class="modal-content data-v-891c2434" catchtap="__e"><view class="modal-header data-v-891c2434"><view class="modal-title data-v-891c2434">用户协议</view><view data-event-opts="{{[['tap',[['closeAgreementModal',['$event']]]]]}}" class="modal-close data-v-891c2434" bindtap="__e">×</view></view><view class="modal-body data-v-891c2434"><view class="agreement-content data-v-891c2434"><text class="data-v-891c2434">1. 用户在使用本服务时，必须遵守相关法律法规。</text><text class="data-v-891c2434">2. 用户应当保护好自己的账号和密码，不得将账号借给他人使用。</text><text class="data-v-891c2434">3. 用户不得利用本服务进行任何违法违规活动。</text><text class="data-v-891c2434">4. 本服务保留对用户行为进行监督和管理的权利。</text><text class="data-v-891c2434">5. 如有违反本协议的行为，本服务有权终止用户的使用权限。</text></view></view><view class="modal-footer data-v-891c2434"><button data-event-opts="{{[['tap',[['closeAgreementModal',['$event']]]]]}}" class="btn btn-primary data-v-891c2434" bindtap="__e">我知道了</button></view></view></view></block><block wx:if="{{showPrivacyModal}}"><view data-event-opts="{{[['tap',[['closePrivacyModal',['$event']]]]]}}" class="modal-overlay data-v-891c2434" bindtap="__e"><view data-event-opts="{{[['tap',[['',['$event']]]]]}}" class="modal-content data-v-891c2434" catchtap="__e"><view class="modal-header data-v-891c2434"><view class="modal-title data-v-891c2434">隐私政策</view><view data-event-opts="{{[['tap',[['closePrivacyModal',['$event']]]]]}}" class="modal-close data-v-891c2434" bindtap="__e">×</view></view><view class="modal-body data-v-891c2434"><view class="privacy-content data-v-891c2434"><text class="data-v-891c2434">1. 我们承诺保护用户的个人隐私信息。</text><text class="data-v-891c2434">2. 用户的个人信息仅用于提供更好的服务体验。</text><text class="data-v-891c2434">3. 我们不会向第三方泄露用户的个人信息。</text><text class="data-v-891c2434">4. 用户有权查看、修改或删除自己的个人信息。</text><text class="data-v-891c2434">5. 如有隐私相关问题，请联系我们的客服团队。</text></view></view><view class="modal-footer data-v-891c2434"><button data-event-opts="{{[['tap',[['closePrivacyModal',['$event']]]]]}}" class="btn btn-primary data-v-891c2434" bindtap="__e">我知道了</button></view></view></view></block><block wx:if="{{showWechatPrivacyModal}}"><view class="modal-overlay privacy-consent-modal data-v-891c2434"><view data-event-opts="{{[['tap',[['',['$event']]]]]}}" class="modal-content privacy-consent-content data-v-891c2434" catchtap="__e"><view class="privacy-consent-header data-v-891c2434"><view class="privacy-icon data-v-891c2434">🔒</view><view class="privacy-title data-v-891c2434">隐私保护提示</view></view><view class="privacy-consent-body data-v-891c2434"><view class="privacy-notice data-v-891c2434"><text class="privacy-text data-v-891c2434">请您仔细阅读</text><text class="privacy-contract-name data-v-891c2434">{{privacyContractName}}</text><text class="privacy-text data-v-891c2434">，了解我们如何收集、使用您的个人信息。</text><text class="privacy-desc data-v-891c2434">我们将严格按照隐私政策保护您的个人信息安全。</text></view></view><view class="privacy-consent-footer data-v-891c2434"><button data-event-opts="{{[['tap',[['handleOpenPrivacyContract',['$event']]]]]}}" class="btn btn-link privacy-btn data-v-891c2434" bindtap="__e">查看隐私协议</button><view class="privacy-action-buttons data-v-891c2434"><button data-event-opts="{{[['tap',[['handleRejectPrivacyAuthorization',['$event']]]]]}}" class="btn btn-secondary privacy-btn data-v-891c2434" bindtap="__e">拒绝</button><button class="btn btn-primary privacy-btn data-v-891c2434" id="agree-privacy-btn" open-type="agreePrivacyAuthorization" data-event-opts="{{[['agreeprivacyauthorization',[['handleAgreePrivacyAuthorization',['$event']]]]]}}" bindagreeprivacyauthorization="__e">同意</button></view></view></view></view></block></view>