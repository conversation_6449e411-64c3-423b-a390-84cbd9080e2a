












































































































































































































































































































































































































































































































































































/* 注册容器 */
.register-container.data-v-891c2434 {
	min-height: 100vh;
	background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
	display: flex;
	align-items: center;
	justify-content: center;
	padding: 40rpx;
	position: relative;
	overflow: hidden;
}
/* 背景装饰 */
.bg-decoration.data-v-891c2434 {
	position: absolute;
	top: 0;
	left: 0;
	right: 0;
	bottom: 0;
	pointer-events: none;
}
.circle.data-v-891c2434 {
	position: absolute;
	border-radius: 50%;
	background: rgba(255, 255, 255, 0.1);
	-webkit-animation: float-data-v-891c2434 6s ease-in-out infinite;
	        animation: float-data-v-891c2434 6s ease-in-out infinite;
}
.circle-1.data-v-891c2434 {
	width: 200rpx;
	height: 200rpx;
	top: 10%;
	left: 10%;
	-webkit-animation-delay: 0s;
	        animation-delay: 0s;
}
.circle-2.data-v-891c2434 {
	width: 150rpx;
	height: 150rpx;
	top: 60%;
	right: 15%;
	-webkit-animation-delay: 2s;
	        animation-delay: 2s;
}
.circle-3.data-v-891c2434 {
	width: 100rpx;
	height: 100rpx;
	bottom: 20%;
	left: 20%;
	-webkit-animation-delay: 4s;
	        animation-delay: 4s;
}
@-webkit-keyframes float-data-v-891c2434 {
0%,
	100% {
		-webkit-transform: translateY(0px) rotate(0deg);
		        transform: translateY(0px) rotate(0deg);
}
50% {
		-webkit-transform: translateY(-20px) rotate(180deg);
		        transform: translateY(-20px) rotate(180deg);
}
}
@keyframes float-data-v-891c2434 {
0%,
	100% {
		-webkit-transform: translateY(0px) rotate(0deg);
		        transform: translateY(0px) rotate(0deg);
}
50% {
		-webkit-transform: translateY(-20px) rotate(180deg);
		        transform: translateY(-20px) rotate(180deg);
}
}
/* 注册卡片 */
.register-card.data-v-891c2434 {
	background: rgba(255, 255, 255, 0.95);
	-webkit-backdrop-filter: blur(10px);
	        backdrop-filter: blur(10px);
	border-radius: 24rpx;
	padding: 60rpx 40rpx;
	width: 100%;
	max-width: 600rpx;
	max-height: 90vh;
	overflow-y: auto;
	box-shadow: 0 20rpx 60rpx rgba(0, 0, 0, 0.1);
	position: relative;
	z-index: 1;
}
.fade-in.data-v-891c2434 {
	-webkit-animation: fadeInUp-data-v-891c2434 0.8s ease-out;
	        animation: fadeInUp-data-v-891c2434 0.8s ease-out;
}
@-webkit-keyframes fadeInUp-data-v-891c2434 {
from {
		opacity: 0;
		-webkit-transform: translateY(50rpx);
		        transform: translateY(50rpx);
}
to {
		opacity: 1;
		-webkit-transform: translateY(0);
		        transform: translateY(0);
}
}
@keyframes fadeInUp-data-v-891c2434 {
from {
		opacity: 0;
		-webkit-transform: translateY(50rpx);
		        transform: translateY(50rpx);
}
to {
		opacity: 1;
		-webkit-transform: translateY(0);
		        transform: translateY(0);
}
}
/* 注册头部 */
.register-header.data-v-891c2434 {
	text-align: center;
	margin-bottom: 50rpx;
}
.logo.data-v-891c2434 {
	font-size: 80rpx;
	margin-bottom: 20rpx;
}
.title.data-v-891c2434 {
	font-size: 48rpx;
	font-weight: bold;
	color: #333333;
	margin-bottom: 10rpx;
}
.subtitle.data-v-891c2434 {
	font-size: 28rpx;
	color: #666666;
}
/* 表单容器 */
.form-container.data-v-891c2434 {
	margin-bottom: 40rpx;
}
/* 输入组 */
.input-group.data-v-891c2434 {
	display: flex;
	align-items: center;
	background: #f8f9fa;
	border-radius: 16rpx;
	padding: 0 20rpx;
	margin-bottom: 30rpx;
	border: 2rpx solid transparent;
	transition: all 0.3s ease;
}
.input-group.data-v-891c2434:focus-within {
	border-color: #667eea;
	background: #ffffff;
	box-shadow: 0 0 0 4rpx rgba(102, 126, 234, 0.1);
}
.input-icon.data-v-891c2434 {
	font-size: 32rpx;
	margin-right: 20rpx;
	color: #999999;
}
.input-field.data-v-891c2434 {
	flex: 1;
	height: 80rpx;
	font-size: 28rpx;
	color: #333333;
	background: transparent;
	border: none;
}
.input-suffix.data-v-891c2434 {
	font-size: 32rpx;
	color: #999999;
	cursor: pointer;
	padding: 10rpx;
}
/* 验证码相关 */
.captcha-group.data-v-891c2434 {
	padding-right: 0;
}
.captcha-input.data-v-891c2434 {
	flex: 1;
	margin-right: 20rpx;
}
.captcha-image.data-v-891c2434 {
	width: 160rpx;
	height: 80rpx;
	border-radius: 8rpx;
	overflow: hidden;
	cursor: pointer;
	display: flex;
	align-items: center;
	justify-content: center;
	background: #e9ecef;
}
.captcha-img.data-v-891c2434 {
	width: 100%;
	height: 100%;
}
.captcha-placeholder.data-v-891c2434 {
	font-size: 24rpx;
	color: #999999;
}
/* 短信验证码 */
.sms-group.data-v-891c2434 {
	padding-right: 0;
}
.sms-input.data-v-891c2434 {
	flex: 1;
	margin-right: 20rpx;
}
.sms-btn.data-v-891c2434 {
	padding: 20rpx 30rpx;
	background: #667eea;
	color: #ffffff;
	border: none;
	border-radius: 12rpx;
	font-size: 24rpx;
	cursor: pointer;
	transition: all 0.3s ease;
}
.sms-btn.data-v-891c2434:disabled {
	background: #c0c4cc;
	cursor: not-allowed;
}
/* 密码强度 */
.password-strength.data-v-891c2434 {
	display: flex;
	align-items: center;
	margin-bottom: 30rpx;
	padding: 0 20rpx;
}
.strength-label.data-v-891c2434 {
	font-size: 24rpx;
	color: #666666;
	margin-right: 15rpx;
}
.strength-bar.data-v-891c2434 {
	display: flex;
	gap: 8rpx;
	margin-right: 15rpx;
}
.strength-item.data-v-891c2434 {
	width: 40rpx;
	height: 8rpx;
	background: #e9ecef;
	border-radius: 4rpx;
	transition: all 0.3s ease;
}
.strength-item.active.data-v-891c2434:nth-child(1) {
	background: #f56c6c;
}
.strength-item.active.data-v-891c2434:nth-child(2) {
	background: #e6a23c;
}
.strength-item.active.data-v-891c2434:nth-child(3) {
	background: #67c23a;
}
.strength-text.data-v-891c2434 {
	font-size: 24rpx;
	color: #666666;
}
/* 协议组 */
.agreement-group.data-v-891c2434 {
	display: flex;
	align-items: flex-start;
	margin-bottom: 40rpx;
	padding: 0 20rpx;
}
.checkbox.data-v-891c2434 {
	width: 36rpx;
	height: 36rpx;
	border: 2rpx solid #ddd;
	border-radius: 6rpx;
	display: flex;
	align-items: center;
	justify-content: center;
	margin-right: 15rpx;
	margin-top: 4rpx;
	cursor: pointer;
	transition: all 0.3s ease;
	flex-shrink: 0;
}
.checkbox.checked.data-v-891c2434 {
	background: #667eea;
	border-color: #667eea;
}
.checkbox-icon.data-v-891c2434 {
	color: #ffffff;
	font-size: 20rpx;
	font-weight: bold;
}
.agreement-text.data-v-891c2434 {
	font-size: 26rpx;
	color: #666666;
	line-height: 1.6;
}
.agreement-link.data-v-891c2434 {
	color: #667eea;
	cursor: pointer;
}
/* 注册按钮 */
.register-btn.data-v-891c2434 {
	width: 100%;
	height: 88rpx;
	background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
	color: #ffffff;
	border: none;
	border-radius: 44rpx;
	font-size: 32rpx;
	font-weight: bold;
	display: flex;
	align-items: center;
	justify-content: center;
	cursor: pointer;
	transition: all 0.3s ease;
	box-shadow: 0 8rpx 24rpx rgba(102, 126, 234, 0.3);
}
.register-btn.data-v-891c2434:hover {
	-webkit-transform: translateY(-2rpx);
	        transform: translateY(-2rpx);
	box-shadow: 0 12rpx 32rpx rgba(102, 126, 234, 0.4);
}
.register-btn.loading.data-v-891c2434,
.register-btn.data-v-891c2434:disabled {
	background: #c0c4cc;
	cursor: not-allowed;
	-webkit-transform: none;
	        transform: none;
	box-shadow: 0 8rpx 24rpx rgba(192, 196, 204, 0.3);
}
.loading-icon.data-v-891c2434 {
	margin-right: 15rpx;
	-webkit-animation: spin-data-v-891c2434 1s linear infinite;
	        animation: spin-data-v-891c2434 1s linear infinite;
}
@-webkit-keyframes spin-data-v-891c2434 {
from {
		-webkit-transform: rotate(0deg);
		        transform: rotate(0deg);
}
to {
		-webkit-transform: rotate(360deg);
		        transform: rotate(360deg);
}
}
@keyframes spin-data-v-891c2434 {
from {
		-webkit-transform: rotate(0deg);
		        transform: rotate(0deg);
}
to {
		-webkit-transform: rotate(360deg);
		        transform: rotate(360deg);
}
}
/* 底部 */
.register-footer.data-v-891c2434 {
	text-align: center;
	margin-top: 40rpx;
}
.footer-link.data-v-891c2434 {
	color: #667eea;
	font-size: 26rpx;
	cursor: pointer;
	transition: all 0.3s ease;
}
.footer-link.data-v-891c2434:hover {
	color: #764ba2;
}
/* 弹窗样式 */
.modal-overlay.data-v-891c2434 {
	position: fixed;
	top: 0;
	left: 0;
	right: 0;
	bottom: 0;
	background-color: rgba(0, 0, 0, 0.5);
	display: flex;
	justify-content: center;
	align-items: center;
	z-index: 1000;
}
.modal-content.data-v-891c2434 {
	background-color: #ffffff;
	border-radius: 16rpx;
	width: 90%;
	max-width: 600rpx;
	max-height: 80vh;
	overflow-y: auto;
	box-shadow: 0 10rpx 40rpx rgba(0, 0, 0, 0.3);
	-webkit-animation: modalSlideIn-data-v-891c2434 0.3s ease-out;
	        animation: modalSlideIn-data-v-891c2434 0.3s ease-out;
}
@-webkit-keyframes modalSlideIn-data-v-891c2434 {
from {
		opacity: 0;
		-webkit-transform: translateY(-50rpx) scale(0.9);
		        transform: translateY(-50rpx) scale(0.9);
}
to {
		opacity: 1;
		-webkit-transform: translateY(0) scale(1);
		        transform: translateY(0) scale(1);
}
}
@keyframes modalSlideIn-data-v-891c2434 {
from {
		opacity: 0;
		-webkit-transform: translateY(-50rpx) scale(0.9);
		        transform: translateY(-50rpx) scale(0.9);
}
to {
		opacity: 1;
		-webkit-transform: translateY(0) scale(1);
		        transform: translateY(0) scale(1);
}
}
.modal-header.data-v-891c2434 {
	display: flex;
	justify-content: space-between;
	align-items: center;
	padding: 30rpx;
	border-bottom: 1rpx solid #f0f0f0;
}
.modal-title.data-v-891c2434 {
	font-size: 32rpx;
	font-weight: bold;
	color: #333333;
}
.modal-close.data-v-891c2434 {
	font-size: 48rpx;
	color: #999999;
	cursor: pointer;
	width: 60rpx;
	height: 60rpx;
	display: flex;
	justify-content: center;
	align-items: center;
	border-radius: 50%;
	transition: all 0.3s ease;
}
.modal-close.data-v-891c2434:hover {
	background-color: #f5f5f5;
	color: #666666;
}
.modal-body.data-v-891c2434 {
	padding: 30rpx;
}
.modal-footer.data-v-891c2434 {
	padding: 30rpx;
	border-top: 1rpx solid #f0f0f0;
	display: flex;
	justify-content: center;
}
.agreement-content.data-v-891c2434,
.privacy-content.data-v-891c2434 {
	line-height: 1.8;
}
.agreement-content text.data-v-891c2434,
.privacy-content text.data-v-891c2434 {
	display: block;
	margin-bottom: 20rpx;
	font-size: 28rpx;
	color: #333333;
}
.btn.data-v-891c2434 {
	padding: 20rpx 40rpx;
	border-radius: 25rpx;
	border: none;
	font-size: 28rpx;
	font-weight: bold;
	cursor: pointer;
	transition: all 0.3s ease;
	text-align: center;
	display: inline-block;
	box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.1);
}
.btn-primary.data-v-891c2434 {
	background: linear-gradient(135deg, #409EFF 0%, #66b1ff 100%);
	color: #ffffff;
}
/* 响应式设计 */
@media screen and (max-width: 750rpx) {
.register-card.data-v-891c2434 {
		padding: 40rpx 30rpx;
}
.title.data-v-891c2434 {
		font-size: 42rpx;
}
.subtitle.data-v-891c2434 {
		font-size: 26rpx;
}
.agreement-text.data-v-891c2434 {
		font-size: 24rpx;
}
}

